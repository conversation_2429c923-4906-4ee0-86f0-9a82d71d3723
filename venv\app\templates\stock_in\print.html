<!DOCTYPE html>
<html>
<head>
    <title>入库单打印</title>
    <meta charset="utf-8">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h2 {
            margin-bottom: 5px;
        }
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .info-table th, .info-table td {
            border: 1px solid #ddd;
            padding: 8px;
        }
        .info-table th {
            width: 25%;
            text-align: right;
            background-color: #f2f2f2;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
        }
        .items-table th, .items-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
        }
        .items-table th {
            background-color: #f2f2f2;
        }
        .footer {
            margin-top: 30px;
            display: flex;
            justify-content: space-between;
        }
        .signature {
            width: 45%;
        }
        @media print {
            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>入库单</h2>
        <p>单号：{{ stock_in.stock_in_number }}</p>
    </div>

    <table class="info-table">
        <tr>
            <th>仓库</th>
            <td>{{ stock_in.warehouse.name }}</td>
            <th>入库日期</th>
            <td>{{  stock_in.stock_in_date|format_datetime('%Y-%m-%d')  }}</td>
        </tr>
        <tr>
            <th>入库类型</th>
            <td>{{ stock_in.stock_in_type }}</td>
            <th>供应商</th>
            <td>{{ stock_in.supplier.name if stock_in.supplier else '-' }}</td>
        </tr>
        <tr>
            <th>操作人</th>
            <td>{{ stock_in.operator.real_name or stock_in.operator.username }}</td>
            <th>状态</th>
            <td>{{ stock_in.status }}</td>
        </tr>
        <tr>
            <th>备注</th>
            <td colspan="3">{{ stock_in.notes or '-' }}</td>
        </tr>
    </table>

    <table class="items-table">
        <thead>
            <tr>
                <th>序号</th>
                <th>食材名称</th>
                <th>存储位置</th>
                <th>批次号/供应商</th>
                <th>数量</th>
                <th>单位</th>
                <th>单价(元)</th>
                <th>总价(元)</th>
                <th>生产日期</th>
                <th>过期日期</th>
            </tr>
        </thead>
        <tbody>
            {% set total_amount = 0 %}
            {% for item in stock_in_items %}
            {% set item_total = (item.quantity|float * (item.unit_price|float if item.unit_price else 0))|round(2) %}
            {% set total_amount = total_amount + item_total %}
            <tr>
                <td>{{ loop.index }}</td>
                <td>{{ item.ingredient.name }}</td>
                <td>{{ item.storage_location.name }} ({{ item.storage_location.location_code }})</td>
                <td>
                    {{ item.batch_number }}
                    {% if item.supplier %}
                    <br><small style="color: #666;">{{ item.supplier.name }}</small>
                    {% endif %}
                </td>
                <td>{{ item.quantity|float|round(2) }}</td>
                <td>{{ item.unit }}</td>
                <td>{{ item.unit_price|float|round(2) if item.unit_price else '-' }}</td>
                <td>{{ item_total }}</td>
                <td>{{ item.production_date|format_datetime('%Y-%m-%d') }}</td>
                <td>{{ item.expiry_date|format_datetime('%Y-%m-%d') }}</td>
            </tr>
            {% endfor %}
        </tbody>
        <tfoot>
            <tr style="font-weight: bold; background-color: #f8f9fa;">
                <td colspan="7" style="text-align: right; padding: 10px;">总计金额:</td>
                <td style="padding: 10px;">{{ total_amount|round(2) }} 元</td>
                <td colspan="2"></td>
            </tr>
        </tfoot>
    </table>

    <div class="footer">
        <div class="signature">
            <p>制单人：{{ stock_in.operator.real_name or stock_in.operator.username }}</p>
            <p>签名：________________</p>
        </div>
        <div class="signature">
            <p>审核人：{{ stock_in.approver.real_name or stock_in.approver.username if stock_in.approver else '________________' }}</p>
            <p>签名：________________</p>
        </div>
    </div>

    <div class="no-print" style="text-align: center; margin-top: 20px;">
        <button onclick="window.print()">打印</button>
        <button onclick="window.close()">关闭</button>
    </div>

    <script>
        // 页面加载完成后自动聚焦
        window.onload = function() {
            window.focus();
        };
    </script>
</body>
</html>
