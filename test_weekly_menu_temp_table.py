#!/usr/bin/env python
"""
测试 weekly_menu_recipes_temp 表的访问
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from sqlalchemy import text

def test_table_access():
    """测试表访问"""
    app = create_app()
    
    with app.app_context():
        try:
            print("1. 测试表是否存在...")
            
            # 检查表是否存在
            check_table_sql = text("""
            SELECT COUNT(*) as table_count
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_NAME = 'weekly_menu_recipes_temp'
            """)
            
            result = db.session.execute(check_table_sql)
            table_count = result.scalar()
            
            if table_count > 0:
                print("✓ 表 weekly_menu_recipes_temp 存在")
            else:
                print("✗ 表 weekly_menu_recipes_temp 不存在")
                return False
            
            print("\n2. 测试表结构...")
            
            # 检查表结构
            check_columns_sql = text("""
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'weekly_menu_recipes_temp'
            ORDER BY ORDINAL_POSITION
            """)
            
            result = db.session.execute(check_columns_sql)
            columns = result.fetchall()
            
            print("表结构:")
            for column in columns:
                print(f"  - {column.COLUMN_NAME}: {column.DATA_TYPE} ({'NULL' if column.IS_NULLABLE == 'YES' else 'NOT NULL'})")
            
            print("\n3. 测试基本查询...")
            
            # 测试基本查询
            test_query_sql = text("""
            SELECT TOP 5 id, weekly_menu_id, day_of_week, meal_type, recipe_name
            FROM weekly_menu_recipes_temp
            """)
            
            result = db.session.execute(test_query_sql)
            rows = result.fetchall()
            
            print(f"查询结果: 找到 {len(rows)} 条记录")
            for row in rows:
                print(f"  - ID: {row.id}, 菜单ID: {row.weekly_menu_id}, 星期: {row.day_of_week}, 餐次: {row.meal_type}, 菜品: {row.recipe_name}")
            
            print("\n4. 测试模型导入...")
            
            # 测试模型导入
            try:
                from app.weekly_menu_recipes_temp import WeeklyMenuRecipesTemp
                print("✓ WeeklyMenuRecipesTemp 模型导入成功")
                
                # 测试模型查询
                temp_recipes = WeeklyMenuRecipesTemp.query.limit(3).all()
                print(f"✓ 模型查询成功，找到 {len(temp_recipes)} 条记录")
                
                for recipe in temp_recipes:
                    print(f"  - 模型记录: ID={recipe.id}, 菜品={recipe.recipe_name}")
                    
            except Exception as model_error:
                print(f"✗ 模型导入或查询失败: {str(model_error)}")
                return False
            
            print("\n5. 测试特定查询（模拟错误场景）...")
            
            # 模拟错误场景的查询
            error_query_sql = text("""
            SELECT day_of_week, meal_type, recipe_id, recipe_name
            FROM weekly_menu_recipes_temp
            WHERE weekly_menu_id = :weekly_menu_id
            """)
            
            # 使用一个存在的菜单ID进行测试
            result = db.session.execute(error_query_sql, {'weekly_menu_id': 25})
            rows = result.fetchall()
            
            print(f"特定查询结果: 菜单ID=25, 找到 {len(rows)} 条记录")
            for row in rows:
                print(f"  - 星期: {row.day_of_week}, 餐次: {row.meal_type}, 菜品ID: {row.recipe_id}, 菜品名: {row.recipe_name}")
            
            print("\n✓ 所有测试通过！")
            return True
            
        except Exception as e:
            print(f"\n✗ 测试失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == '__main__':
    success = test_table_access()
    if success:
        print("\n表访问测试成功！")
    else:
        print("\n表访问测试失败！")
        sys.exit(1)
