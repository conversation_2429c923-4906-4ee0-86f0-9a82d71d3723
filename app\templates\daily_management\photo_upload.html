<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ title }}</title>

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='bootstrap/css/bootstrap.min.css') }}">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/fontawesome/css/all.min.css') }}">

    <style>
        body {
            font-family: "Microsoft YaHei", "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f8f9fc;
            padding-bottom: 60px;
        }

        .header {
            background: linear-gradient(135deg, #4e73df, #224abe);
            color: white;
            padding: 1rem;
            text-align: center;
            position: relative;
        }

        .header h1 {
            font-size: 1.5rem;
            margin: 0;
        }

        .header .school-name {
            font-size: 1rem;
            margin-top: 0.5rem;
            opacity: 0.9;
        }

        .content {
            padding: 1rem;
        }

        .card {
            border-radius: 0.5rem;
            box-shadow: 0 0.15rem 1.75rem rgba(58, 59, 69, 0.1);
            margin-bottom: 1.5rem;
        }

        .card .card-header {
            background: linear-gradient(135deg, #f8f9fc, #ffffff);
            border-bottom: 1px solid rgba(227, 230, 240, 0.5);
            padding: 1rem 1.25rem;
        }

        .card .card-header h2 {
            font-size: 1.25rem;
            margin: 0;
            color: #4e73df;
        }

        .upload-zone {
            border: 2px dashed #d1d3e2;
            border-radius: 0.5rem;
            padding: 2rem;
            text-align: center;
            margin-bottom: 1.5rem;
            cursor: pointer;
            transition: all 0.3s;
        }

        .upload-zone:hover {
            border-color: #4e73df;
            background-color: #f8f9fc;
        }

        .upload-zone i {
            font-size: 3rem;
            color: #4e73df;
            margin-bottom: 1rem;
        }

        .upload-zone p {
            margin-bottom: 0;
            color: #858796;
        }

        .upload-zone input[type="file"] {
            display: none;
        }

        .preview-container {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-top: 1rem;
        }

        .preview-item {
            position: relative;
            width: 100px;
            height: 100px;
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 0.15rem 0.5rem rgba(58, 59, 69, 0.1);
        }

        .preview-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .preview-item .remove-btn {
            position: absolute;
            top: 0.25rem;
            right: 0.25rem;
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 50%;
            width: 1.5rem;
            height: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #e74a3b;
            cursor: pointer;
        }

        .btn-primary {
            background-color: #4e73df;
            border-color: #4e73df;
        }

        .btn-primary:hover {
            background-color: #2e59d9;
            border-color: #2653d4;
        }

        .btn-success {
            background-color: #1cc88a;
            border-color: #1cc88a;
        }

        .btn-success:hover {
            background-color: #17a673;
            border-color: #169b6b;
        }

        .time-selector {
            margin-bottom: 1.5rem;
        }

        .time-selector .btn-group {
            width: 100%;
        }

        .time-selector .btn {
            flex: 1;
            border-radius: 0.5rem;
            padding: 0.75rem 0;
            font-weight: 600;
        }

        .time-selector .btn.active {
            box-shadow: none;
        }

        .success-message {
            text-align: center;
            padding: 2rem;
        }

        .success-message i {
            font-size: 4rem;
            color: #1cc88a;
            margin-bottom: 1rem;
        }

        .success-message h2 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }

        .success-message p {
            color: #858796;
            margin-bottom: 2rem;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .loading-overlay .spinner-border {
            width: 3rem;
            height: 3rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>校园食堂智能监管平台</h1>
        <div class="school-name">{{ school.name }}</div>
    </div>

    <div class="content">
        <div id="uploadForm">
            <div class="card">
                <div class="card-header">
                    <h2>上传检查照片</h2>
                </div>
                <div class="card-body">
                    <div class="time-selector">
                        <label>选择检查时段：</label>
                        <div class="btn-group btn-group-toggle" data-toggle="buttons">
                            <label class="btn btn-outline-primary active">
                                <input type="radio" name="inspection_time" value="morning" checked> 早晨
                            </label>
                            <label class="btn btn-outline-primary">
                                <input type="radio" name="inspection_time" value="noon"> 中午
                            </label>
                            <label class="btn btn-outline-primary">
                                <input type="radio" name="inspection_time" value="evening"> 晚上
                            </label>
                        </div>
                    </div>

                    <div class="upload-zone" id="uploadZone">
                        <i class="fas fa-camera"></i>
                        <h4>拍照上传</h4>
                        <p>点击此处拍照或选择照片</p>
                        <input type="file" id="photoInput" multiple accept="image/*" capture="environment">
                    </div>

                    <div class="preview-container" id="previewContainer"></div>

                    <button type="button" class="btn btn-primary btn-block" id="submitBtn" disabled>
                        <i class="fas fa-upload mr-1"></i> 上传照片
                    </button>
                </div>
            </div>
        </div>

        <div id="successMessage" style="display: none;">
            <div class="success-message">
                <i class="fas fa-check-circle"></i>
                <h2>上传成功！</h2>
                <p>感谢您的配合，照片已成功上传。</p>
                <button type="button" class="btn btn-success" id="uploadMoreBtn">
                    <i class="fas fa-camera mr-1"></i> 继续上传
                </button>
            </div>
        </div>
    </div>

    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="spinner-border text-primary" role="status">
            <span class="sr-only">上传中...</span>
        </div>
        <p>正在上传照片，请稍候...</p>
    </div>

    <!-- JavaScript -->
    <script src="{{ url_for('static', filename='vendor/jquery/jquery.min.js') }}"></script>
    <script src="{{ url_for('static', filename='bootstrap/js/bootstrap.bundle.min.js') }}"></script>
    <script>
        $(document).ready(function() {
            const uploadZone = document.getElementById('uploadZone');
            const photoInput = document.getElementById('photoInput');
            const previewContainer = document.getElementById('previewContainer');
            const submitBtn = document.getElementById('submitBtn');
            const uploadForm = document.getElementById('uploadForm');
            const successMessage = document.getElementById('successMessage');
            const uploadMoreBtn = document.getElementById('uploadMoreBtn');
            const loadingOverlay = document.getElementById('loadingOverlay');

            let selectedFiles = [];

            // 点击上传区域触发文件选择
            uploadZone.addEventListener('click', function() {
                photoInput.click();
            });

            // 文件选择变化时预览图片
            photoInput.addEventListener('change', function() {
                if (this.files.length > 0) {
                    for (let i = 0; i < this.files.length; i++) {
                        const file = this.files[i];
                        selectedFiles.push(file);

                        // 创建预览
                        const previewItem = document.createElement('div');
                        previewItem.className = 'preview-item';

                        const img = document.createElement('img');
                        img.src = URL.createObjectURL(file);

                        const removeBtn = document.createElement('div');
                        removeBtn.className = 'remove-btn';
                        removeBtn.innerHTML = '<i class="fas fa-times"></i>';
                        removeBtn.dataset.index = selectedFiles.length - 1;

                        removeBtn.addEventListener('click', function(e) {
                            e.stopPropagation();
                            const index = parseInt(this.dataset.index);
                            selectedFiles.splice(index, 1);
                            previewItem.remove();

                            // 更新所有移除按钮的索引
                            document.querySelectorAll('.remove-btn').forEach((btn, i) => {
                                btn.dataset.index = i;
                            });

                            // 更新提交按钮状态
                            submitBtn.disabled = selectedFiles.length === 0;
                        });

                        previewItem.appendChild(img);
                        previewItem.appendChild(removeBtn);
                        previewContainer.appendChild(previewItem);
                    }

                    // 启用提交按钮
                    submitBtn.disabled = false;
                }
            });

            // 提交表单
            submitBtn.addEventListener('click', function() {
                if (selectedFiles.length === 0) {
                    alert('请选择至少一张照片');
                    return;
                }

                // 显示加载中
                loadingOverlay.style.display = 'flex';

                // 获取选中的检查时段
                const inspectionTime = document.querySelector('input[name="inspection_time"]:checked').value;

                // 创建表单数据
                const formData = new FormData();

                // 添加照片文件
                for (let i = 0; i < selectedFiles.length; i++) {
                    formData.append('file', selectedFiles[i]);
                }

                // 添加参数
                formData.append('reference_type', inspectionTime);
                formData.append('reference_id', {{ log_id }});

                // 发送上传请求
                $.ajax({
                    url: '/api/v2/photos/' + inspectionTime + '/' + {{ log_id }},
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        // 隐藏加载中
                        loadingOverlay.style.display = 'none';

                        // 显示成功消息
                        uploadForm.style.display = 'none';
                        successMessage.style.display = 'block';
                    },
                    error: function(xhr) {
                        // 隐藏加载中
                        loadingOverlay.style.display = 'none';

                        let errorMessage = '上传照片失败';

                        try {
                            const response = JSON.parse(xhr.responseText);
                            if (response.error) {
                                errorMessage = response.error;
                            }
                        } catch (e) {}

                        alert(errorMessage);
                    }
                });
            });

            // 继续上传按钮
            uploadMoreBtn.addEventListener('click', function() {
                // 重置表单
                selectedFiles = [];
                previewContainer.innerHTML = '';
                submitBtn.disabled = true;

                // 显示上传表单
                uploadForm.style.display = 'block';
                successMessage.style.display = 'none';
            });
        });
    </script>
</body>
</html>
