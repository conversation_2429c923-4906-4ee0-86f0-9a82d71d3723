{% extends 'base.html' %}

{% block title %}简易周菜单安排 - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row mb-4">
    <div class="col-md-8">
      <h2>简易周菜单安排</h2>
      <p class="text-muted">使用系统自带的表单处理功能，无需复杂的JavaScript</p>
    </div>
    <div class="col-md-4 text-right">
      <a href="{{ url_for('weekly_menu_v2.print', area_id=area_id, week_start=week_start) }}" class="btn btn-info" target="_blank">
        <i class="fas fa-print"></i> 打印菜单
      </a>
    </div>
  </div>

  <div class="row">
    <!-- 筛选条件 -->
    <div class="col-md-3 mb-4">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">筛选条件</h5>
        </div>
        <div class="card-body">
          <form method="get">
            <div class="form-group">
              <label for="area_id">区域</label>
              <select class="form-control" id="area_id" name="area_id">
                {% for area in areas %}
                <option value="{{ area.id }}" {% if area.id == area_id %}selected{% endif %}>
                  {{ area.get_level_name() }} - {{ area.name }}
                </option>
                {% endfor %}
              </select>
            </div>
            <div class="form-group">
              <label for="week_start">周开始日期</label>
              <input type="date" class="form-control" id="week_start" name="week_start" value="{{ week_start }}">
            </div>
            <button type="submit" class="btn btn-primary btn-block">应用筛选</button>
          </form>
        </div>
      </div>
    </div>

    <!-- 周菜单表格 -->
    <div class="col-md-9">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">周菜单安排</h5>
        </div>
        <div class="card-body">
          <form method="post" action="{{ url_for('menu_plan.save_simple_planner') }}">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
            <input type="hidden" name="area_id" value="{{ area_id }}">
            <input type="hidden" name="week_start" value="{{ week_start }}">

            <table class="table table-bordered">
              <thead>
                <tr>
                  <th style="width: 15%">日期</th>
                  <th style="width: 28%">早餐</th>
                  <th style="width: 28%">午餐</th>
                  <th style="width: 28%">晚餐</th>
                </tr>
              </thead>
              <tbody>
                {% for date_str, day_data in week_dates.items() %}
                <tr>
                  <td>
                    <div class="font-weight-bold">{{ day_data.weekday }}</div>
                    <div>{{ date_str }}</div>
                  </td>
                  {% for meal_type in ['早餐', '午餐', '晚餐'] %}
                  <td>
                    <div class="form-group">
                      <label>{{ meal_type }}</label>
                      <select class="form-control" name="menu[{{ date_str }}][{{ meal_type }}][]" multiple size="5">
                        {% for recipe in recipes %}
                        <option value="{{ recipe.id }}"
                          {% if existing_menu.get(date_str, {}).get(meal_type) and
                                recipe.id in [r.id for r in existing_menu[date_str][meal_type]] %}
                            selected
                          {% endif %}>
                          {{ recipe.name }}
                        </option>
                        {% endfor %}
                      </select>
                      <small class="form-text text-muted">按住Ctrl键可多选</small>
                    </div>
                  </td>
                  {% endfor %}
                </tr>
                {% endfor %}
              </tbody>
            </table>

            <div class="text-center mt-4">
              <button type="submit" class="btn btn-primary btn-lg">
                <i class="fas fa-save"></i> 保存菜单
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
