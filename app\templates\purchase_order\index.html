{% extends 'base.html' %}

{% block title %}采购订单列表 - {{ super() }}{% endblock %}

{% block styles %}
{{ super() }}
<style>
.order-status {
    font-size: 0.85em;
    padding: 0.25em 0.6em;
    border-radius: 0.25rem;
}
.status-pending, .status-待确认 {
    background-color: #ffc107;
    color: #000;
}
.status-confirmed, .status-已确认 {
    background-color: #17a2b8;
    color: #fff;
}
.status-delivered, .status-已送达 {
    background-color: #28a745;
    color: #fff;
}
.status-cancelled, .status-已取消 {
    background-color: #dc3545;
    color: #fff;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题和操作按钮 -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2>采购订单管理</h2>
        </div>
        <div class="col-md-4 text-right">
            <div class="btn-group">
                <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#createFromMenuModal">
                    <i class="fas fa-plus"></i> 从周菜单创建
                </button>
                <a href="{{ url_for('purchase_order.create_form') }}" class="btn btn-outline-primary">
                    <i class="fas fa-edit"></i> 手动创建
                </a>
            </div>
        </div>
    </div>

    <!-- 筛选工具栏 -->
    <div class="card mb-4">
        <div class="card-body">
            <form id="filterForm" class="row">
                <div class="col-md-3 mb-2">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">订单号</span>
                        </div>
                        <input type="text" class="form-control" name="order_number" placeholder="输入订单号搜索">
                    </div>
                </div>
                <div class="col-md-2 mb-2">
                    <select class="form-control" name="status">
                        <option value="">所有状态</option>
                        <option value="待确认">待确认</option>
                        <option value="已确认">已确认</option>
                        <option value="已送达">已送达</option>
                        <option value="已取消">已取消</option>
                    </select>
                </div>
                <div class="col-md-3 mb-2">
                    <div class="input-group">
                        <div class="input-group-prepend">
                            <span class="input-group-text">日期范围</span>
                        </div>
                        <input type="date" class="form-control" name="start_date">
                        <input type="date" class="form-control" name="end_date">
                    </div>
                </div>
                <div class="col-md-2 mb-2">
                    <button type="submit" class="btn btn-primary btn-block">
                        <i class="fas fa-search"></i> 搜索
                    </button>
                </div>
                <div class="col-md-2 mb-2">
                    <button type="reset" class="btn btn-secondary btn-block">
                        <i class="fas fa-redo"></i> 重置
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- 订单列表 -->
    <div class="card">
        <div class="card-body">
            <!-- 表格工具栏 -->
            <div class="table-toolbar">
                <div class="toolbar-left">
                    <h6 class="mb-0">
                        <i class="fas fa-list"></i> 采购订单列表
                        <span class="badge badge-primary ml-2">{{ orders.items|length }} 条记录</span>
                    </h6>
                </div>
                <div class="toolbar-right">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        表格已优化显示，支持紧凑排版
                    </small>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-hover table-compact">
                    <thead>
                        <tr>
                            <th style="width: 140px;">订单号</th>
                            <th style="width: 100px;">区域</th>
                            <th style="width: 120px;">创建时间</th>
                            <th style="width: 100px;">送货日期</th>
                            <th style="width: 90px;" class="number-column">总金额</th>
                            <th style="width: 80px;">状态</th>
                            <th style="width: 200px;" class="action-column">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for order in orders.items %}
                        <tr>
                            <td>
                                <a href="{{ url_for('purchase_order.view', id=order.id) }}" class="text-primary font-weight-medium">
                                    {{ order.order_number }}
                                </a>
                            </td>
                            <td class="text-content">{{ order.area.name }}</td>
                            <td class="datetime-column">
                                <div class="datetime-display">
                                    <span class="date">{{ order.order_date|format_datetime('%m-%d') if order.order_date else '-' }}</span>
                                    <span class="time">{{ order.order_date|format_datetime('%H:%M') if order.order_date else '' }}</span>
                                </div>
                            </td>
                            <td class="datetime-column">{{ order.delivery_date|format_datetime('%m-%d') if order.delivery_date else '-' }}</td>
                            <td class="number-column">
                                <span class="amount-display">¥{{ "%.2f"|format(order.total_amount) }}</span>
                            </td>
                            <td>
                                {% if order.status == '待确认' %}
                                <span class="status-badge status-warning">{{ order.get_status_display() }}</span>
                                {% elif order.status == '已确认' %}
                                <span class="status-badge status-info">{{ order.get_status_display() }}</span>
                                {% elif order.status == '已送达' %}
                                <span class="status-badge status-success">{{ order.get_status_display() }}</span>
                                {% elif order.status == '已取消' %}
                                <span class="status-badge status-danger">{{ order.get_status_display() }}</span>
                                {% else %}
                                <span class="status-badge">{{ order.get_status_display() }}</span>
                                {% endif %}
                            </td>
                            <td class="action-column">
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('purchase_order.view', id=order.id) }}"
                                       class="btn btn-outline-primary"
                                       title="查看详情">
                                        <i class="fas fa-eye"></i>
                                    </a>

                                    {% if current_user.is_admin() or current_user.has_role('学校管理员') %}
                                    <!-- 学校管理员或系统管理员可以执行所有操作 -->
                                    <button type="button"
                                            class="btn btn-outline-success confirm-btn"
                                            data-id="{{ order.id }}"
                                            title="确认订单">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button type="button"
                                            class="btn btn-outline-danger cancel-btn"
                                            data-id="{{ order.id }}"
                                            title="取消订单">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    <button type="button"
                                            class="btn btn-outline-info deliver-btn"
                                            data-id="{{ order.id }}"
                                            title="标记送达">
                                        <i class="fas fa-truck"></i>
                                    </button>
                                    <!-- 删除按钮：只有未入库的订单可以删除 -->
                                    {% if order.id not in stock_in_order_ids and order.status != '已入库' %}
                                    <button type="button"
                                            class="btn btn-outline-danger delete-btn"
                                            data-id="{{ order.id }}"
                                            title="删除订单">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                    {% endif %}
                                    {% else %}
                                    <!-- 普通用户只能根据状态执行特定操作 -->
                                    {% if order.status == '待确认' %}
                                    <button type="button"
                                            class="btn btn-outline-success confirm-btn"
                                            data-id="{{ order.id }}"
                                            title="确认订单">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button type="button"
                                            class="btn btn-outline-danger cancel-btn"
                                            data-id="{{ order.id }}"
                                            title="取消订单">
                                        <i class="fas fa-times"></i>
                                    </button>
                                    {% endif %}
                                    {% if order.status == '已确认' %}
                                    <button type="button"
                                            class="btn btn-outline-info deliver-btn"
                                            data-id="{{ order.id }}"
                                            title="标记送达">
                                        <i class="fas fa-truck"></i>
                                    </button>
                                    {% endif %}

                                    <!-- 删除按钮：未入库的订单可以删除 -->
                                    {% if order.status in ['待确认', '已取消'] and order.id not in stock_in_order_ids %}
                                    <button type="button"
                                            class="btn btn-outline-danger delete-btn"
                                            data-id="{{ order.id }}"
                                            title="删除订单">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                    {% endif %}
                                    {% endif %}

                                    {# 所有状态的订单都可以创建入库单 #}
                                    {% if order.id in stock_in_order_ids %}
                                    <a href="{{ url_for('stock_in.index') }}"
                                       class="btn btn-outline-secondary"
                                       title="已创建入库单">
                                        <i class="fas fa-check-circle"></i>
                                    </a>
                                    {% else %}
                                    <a href="{{ url_for('stock_in_wizard.create_from_purchase_get', purchase_order_id=order.id) }}"
                                       class="btn btn-outline-success"
                                       title="一键入库">
                                        <i class="fas fa-dolly"></i>
                                    </a>
                                    {% endif %}
                                    <a href="{{ url_for('purchase_order.print_order', order_id=order.id) }}"
                                       class="btn btn-outline-secondary"
                                       target="_blank"
                                       title="打印订单">
                                        <i class="fas fa-print"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="7" class="table-empty">
                                <i class="fas fa-inbox"></i>
                                <h5>暂无采购订单</h5>
                                <p>您可以创建新的采购订单或调整筛选条件</p>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 分页导航 -->
            {% if orders.pages > 1 %}
            <nav aria-label="采购订单分页">
                <ul class="pagination justify-content-center">
                    <!-- 上一页 -->
                    {% if orders.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('purchase_order.index', page=orders.prev_num, **request.args) }}">
                            <i class="fas fa-chevron-left"></i> 上一页
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">
                            <i class="fas fa-chevron-left"></i> 上一页
                        </span>
                    </li>
                    {% endif %}

                    <!-- 页码 -->
                    {% for page_num in orders.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != orders.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('purchase_order.index', page=page_num, **request.args) }}">{{ page_num }}</a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                        {% endif %}
                    {% endfor %}

                    <!-- 下一页 -->
                    {% if orders.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('purchase_order.index', page=orders.next_num, **request.args) }}">
                            下一页 <i class="fas fa-chevron-right"></i>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">
                            下一页 <i class="fas fa-chevron-right"></i>
                        </span>
                    </li>
                    {% endif %}
                </ul>

                <!-- 分页信息 -->
                <div class="text-center mt-2">
                    <small class="text-muted">
                        显示第 {{ (orders.page - 1) * orders.per_page + 1 }} - {{ orders.page * orders.per_page if orders.page * orders.per_page <= orders.total else orders.total }} 条，
                        共 {{ orders.total }} 条记录，第 {{ orders.page }} / {{ orders.pages }} 页
                    </small>
                </div>
            </nav>
            {% endif %}
        </div>
    </div>
</div>

<!-- 从周菜单创建模态框 -->
<div class="modal fade" id="createFromMenuModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">选择采购区域</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="createFromMenuForm">
                    <div class="form-group">
                        <label for="areaSelect">请选择区域：</label>
                        <select class="form-control" id="areaSelect" required>
                            <option value="">请选择...</option>
                            {% for area in areas %}
                            <option value="{{ area.id }}">{{ area.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmAreaBtn">
                    <i class="fas fa-arrow-right"></i> 下一步
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 确认模态框 -->
<div class="modal fade" id="confirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认订单</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>确定要确认这个采购订单吗？确认后将通知供应商开始备货。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-success" id="confirmOrderBtn">
                    <i class="fas fa-check"></i> 确认
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 取消模态框 -->
<div class="modal fade" id="cancelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">取消订单</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>确定要取消这个采购订单吗？取消后将无法恢复。</p>
                <div class="form-group">
                    <label for="cancelReason">取消原因：</label>
                    <textarea class="form-control" id="cancelReason" rows="3" required></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-danger" id="cancelOrderBtn">
                    <i class="fas fa-times"></i> 确定取消
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 送达模态框 -->
<div class="modal fade" id="deliverModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">标记送达</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>确认所有食材已送达并验收无误？</p>
                <div class="form-group">
                    <label for="deliveryNotes">备注（可选）：</label>
                    <textarea class="form-control" id="deliveryNotes" rows="3"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-info" id="deliverOrderBtn">
                    <i class="fas fa-truck"></i> 确认送达
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 删除模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">删除订单</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> 警告：此操作不可恢复！
                </div>
                <p>确定要<strong>永久删除</strong>这个采购订单吗？删除后将无法恢复。</p>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> 注意：只有未入库的订单才能删除。已入库或已创建入库单的订单无法删除。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="deleteOrderBtn">
                    <i class="fas fa-trash-alt"></i> 确认删除
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
$(document).ready(function() {
    let currentOrderId = null;

    // 初始化日期范围
    const today = new Date();
    const thirtyDaysAgo = new Date(today);
    thirtyDaysAgo.setDate(today.getDate() - 30);

    $('input[name="start_date"]').val(thirtyDaysAgo.toISOString().split('T')[0]);
    $('input[name="end_date"]').val(today.toISOString().split('T')[0]);

    // 确认订单
    $('.confirm-btn').click(function() {
        currentOrderId = $(this).data('id');
        $('#confirmModal').modal('show');
    });

    $('#confirmOrderBtn').click(function() {
        if (!currentOrderId) return;

        const btn = $(this);
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 处理中...');

        $.ajax({
            url: `/purchase-order/${currentOrderId}/confirm`,
            type: 'POST',
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.message);
                }
            },
            error: function() {
                alert('确认订单失败，请重试');
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-check"></i> 确认');
                $('#confirmModal').modal('hide');
            }
        });
    });

    // 取消订单
    $('.cancel-btn').click(function() {
        currentOrderId = $(this).data('id');
        $('#cancelModal').modal('show');
    });

    $('#cancelOrderBtn').click(function() {
        if (!currentOrderId) return;

        const reason = $('#cancelReason').val().trim();
        if (!reason) {
            alert('请输入取消原因');
            return;
        }

        const btn = $(this);
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 处理中...');

        $.ajax({
            url: `/purchase-order/${currentOrderId}/cancel`,
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ reason: reason }),
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.message);
                }
            },
            error: function() {
                alert('取消订单失败，请重试');
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-times"></i> 确定取消');
                $('#cancelModal').modal('hide');
            }
        });
    });

    // 标记送达
    $('.deliver-btn').click(function() {
        currentOrderId = $(this).data('id');
        $('#deliverModal').modal('show');
    });

    $('#deliverOrderBtn').click(function() {
        if (!currentOrderId) return;

        const notes = $('#deliveryNotes').val().trim();
        const btn = $(this);
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 处理中...');

        $.ajax({
            url: `/purchase-order/${currentOrderId}/deliver`,
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ notes: notes }),
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.message);
                }
            },
            error: function() {
                alert('标记送达失败，请重试');
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-truck"></i> 确认送达');
                $('#deliverModal').modal('hide');
            }
        });
    });

    // 删除订单
    $('.delete-btn').click(function() {
        currentOrderId = $(this).data('id');
        $('#deleteModal').modal('show');
    });

    $('#deleteOrderBtn').click(function() {
        if (!currentOrderId) return;

        const btn = $(this);
        btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 处理中...');

        $.ajax({
            url: `/purchase-order/${currentOrderId}/delete`,
            type: 'POST',
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.message);
                }
            },
            error: function() {
                alert('删除订单失败，请重试');
            },
            complete: function() {
                btn.prop('disabled', false).html('<i class="fas fa-trash-alt"></i> 确认删除');
                $('#deleteModal').modal('hide');
            }
        });
    });

    // 筛选表单提交
    $('#filterForm').submit(function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const params = new URLSearchParams();

        for (const [key, value] of formData.entries()) {
            if (value) {
                params.append(key, value);
            }
        }

        window.location.search = params.toString();
    });

    // 重置筛选
    $('#filterForm button[type="reset"]').click(function() {
        window.location.href = window.location.pathname;
    });

    // 确认区域选择
    $('#confirmAreaBtn').click(function() {
        const areaId = $('#areaSelect').val();
        if (!areaId) {
            alert('请选择区域');
            return;
        }
        window.location.href = '{{ url_for("purchase_order.create_from_menu") }}?area_id=' + areaId;
    });
});
</script>
{% endblock %}
