#!/usr/bin/env python3
"""
解除自己IP阻止的脚本
"""

import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.security_config import get_blocked_ips, unblock_ip, ip_access_log

def main():
    """解除本机IP的阻止"""
    app = create_app()
    
    with app.app_context():
        print("🔍 当前被阻止的IP列表:")
        blocked_ips = get_blocked_ips()
        
        if not blocked_ips:
            print("  ✅ 没有被阻止的IP")
            return
        
        for i, ip in enumerate(blocked_ips, 1):
            print(f"  {i}. {ip}")
        
        # 常见的本机IP
        local_ips = ['127.0.0.1', 'localhost', '::1', '***********', '***********']
        
        # 解除本机相关的IP
        unblocked_count = 0
        for ip in blocked_ips.copy():  # 使用copy避免在迭代时修改集合
            if any(local_ip in ip for local_ip in local_ips) or ip.startswith('192.168.') or ip.startswith('10.') or ip.startswith('172.'):
                print(f"🔓 解除阻止: {ip}")
                unblock_ip(ip)
                unblocked_count += 1
        
        if unblocked_count == 0:
            print("\n❓ 没有发现本机IP被阻止")
            print("如果您知道自己的IP，可以手动解除:")
            print("1. 访问 http://localhost:5000/security/dashboard")
            print("2. 在安全仪表盘中手动解除阻止")
        else:
            print(f"\n✅ 已解除 {unblocked_count} 个本机IP的阻止")
        
        # 清空访问日志（可选）
        print("\n🧹 是否要清空访问日志？(y/n): ", end="")
        choice = input().lower().strip()
        if choice in ['y', 'yes', '是']:
            ip_access_log.clear()
            print("✅ 访问日志已清空")
        
        print("\n🎉 操作完成！现在可以正常访问系统了。")

if __name__ == "__main__":
    main()
