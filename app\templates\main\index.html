{% extends 'base_landing.html' %}

{% block title %}校园餐智慧食堂平台 - {{ super() }}{% endblock %}

{% block extra_css %}
<script src="https://cdn.tailwindcss.com"></script>
<link href="{{ url_for('static', filename='css/font-awesome.min.css') }}" rel="stylesheet">
<script src="{{ url_for('static', filename='js/chart.umd.min.js') }}"></script>

<!-- 配置Tailwind CSS -->
<script>
  tailwind.config = {
    theme: {
      extend: {
        colors: {
          primary: '#165DFF',
          secondary: '#36CBCB',
          accent: '#722ED1',
          dark: '#1D2129',
          light: '#F7F8FA',
          'primary-light': '#E8F3FF',
          'primary-dark': '#0D47A1',
          'neon-blue': '#00BFFF',
          'neon-purple': '#9D4EDD',
          'neon-green': '#00FF9D',
          'dark-blue': '#0B0E2F',
        },
        fontFamily: {
          inter: ['Inter', 'system-ui', 'sans-serif'],
          code: ['JetBrains Mono', 'monospace'],
        },
      },
    }
  }
</script>

<style>
  /* 自定义样式 */
  .content-auto {
    content-visibility: auto;
  }
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  .card-hover {
    transition: all 0.3s ease;
  }
  .card-hover:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    transform: translateY(-0.25rem);
  }
  .section-padding {
    padding-top: 4rem;
    padding-bottom: 4rem;
  }
  @media (min-width: 768px) {
    .section-padding {
      padding-top: 6rem;
      padding-bottom: 6rem;
    }
  }
  .bg-gradient-primary {
    background: linear-gradient(to right, #165DFF, #0D47A1);
  }
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
  .neon-glow {
    box-shadow: 0 0 10px rgba(0, 191, 255, 0.5), 0 0 20px rgba(0, 191, 255, 0.3);
  }
  .neon-text {
    text-shadow: 0 0 10px rgba(0, 191, 255, 0.7);
  }
  .data-pulse {
    animation: pulse 2s infinite;
  }
  @keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
    100% { transform: translateY(0px); }
  }
  @keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.05); opacity: 0.8; }
    100% { transform: scale(1); opacity: 1; }
  }
  .bg-grid {
    background-image: linear-gradient(rgba(22, 93, 255, 0.05) 1px, transparent 1px),
                      linear-gradient(90deg, rgba(22, 93, 255, 0.05) 1px, transparent 1px);
    background-size: 20px 20px;
  }
  .clip-path-slant {
    clip-path: polygon(0 0, 100% 0, 100% 85%, 0 100%);
  }

  /* 缺少的样式 */
  .text-xs { font-size: 0.75rem; }
  .w-2 { width: 0.5rem; }
  .h-2 { height: 0.5rem; }
  .w-60 { width: 15rem; }
  .h-60 { height: 15rem; }
  .w-64 { width: 16rem; }
  .h-64 { height: 16rem; }
  .w-80 { width: 20rem; }
  .h-80 { height: 20rem; }
  .-top-40 { top: -10rem; }
  .-left-40 { left: -10rem; }
  .-bottom-40 { bottom: -10rem; }
  .-right-40 { right: -10rem; }
  .-left-20 { left: -5rem; }
  .-right-20 { right: -5rem; }
  .-left-32 { left: -8rem; }
  .-right-32 { right: -8rem; }
  .top-1\/4 { top: 25%; }
  .bottom-1\/4 { bottom: 25%; }
  .top-1\/3 { top: 33.333333%; }
  .bottom-1\/3 { bottom: 33.333333%; }
  .left-1\/2 { left: 50%; }
  .transform { transform: translateZ(0); }
  .-translate-x-1\/2 { transform: translateX(-50%); }
  .blur-3xl { filter: blur(64px); }
  .opacity-50 { opacity: 0.5; }

  /* 响应式文本大小 */
  .text-\[clamp\(2rem\,5vw\,3\.5rem\)\] { font-size: clamp(2rem, 5vw, 3.5rem); }
  .text-\[clamp\(1\.8rem\,4vw\,2\.8rem\)\] { font-size: clamp(1.8rem, 4vw, 2.8rem); }

  /* 间距 */
  .gap-4 { gap: 1rem; }
  .space-y-12 > * + * { margin-top: 3rem; }
  .space-y-6 > * + * { margin-top: 1.5rem; }

  /* 边框和颜色 */
  .border-neon-blue\/30 { border-color: rgba(0, 191, 255, 0.3); }
  .border-neon-blue\/50 { border-color: rgba(0, 191, 255, 0.5); }
  .bg-neon-blue\/20 { background-color: rgba(0, 191, 255, 0.2); }
  .bg-neon-purple\/20 { background-color: rgba(157, 78, 221, 0.2); }
  .bg-neon-green\/20 { background-color: rgba(0, 255, 157, 0.2); }
  .bg-neon-blue\/10 { background-color: rgba(0, 191, 255, 0.1); }
  .bg-neon-purple\/10 { background-color: rgba(157, 78, 221, 0.1); }

  /* 渐变 */
  .bg-gradient-to-b { background: linear-gradient(to bottom, var(--tw-gradient-stops)); }
  .from-primary\/5 { --tw-gradient-from: rgba(22, 93, 255, 0.05); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(22, 93, 255, 0)); }
  .from-neon-blue { --tw-gradient-from: #00BFFF; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(0, 191, 255, 0)); }
  .to-neon-purple { --tw-gradient-to: #9D4EDD; }
  .from-dark-blue { --tw-gradient-from: #0B0E2F; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(11, 14, 47, 0)); }
  .to-dark { --tw-gradient-to: #1D2129; }

  /* 其他 */
  .order-1 { order: 1; }
  .order-2 { order: 2; }
  .order-3 { order: 3; }
  .md\\:order-2 { order: 2; }
  .md\\:order-3 { order: 3; }
  @media (min-width: 768px) {
    .md\\:order-2 { order: 2; }
    .md\\:order-3 { order: 3; }
    .md\\:text-right { text-align: right; }
    .md\\:pr-12 { padding-right: 3rem; }
    .md\\:pl-12 { padding-left: 3rem; }
    .md\\:p-12 { padding: 3rem; }
  }

  /* 焦点样式 */
  .focus\\:ring-2:focus { box-shadow: 0 0 0 2px rgba(0, 191, 255, 0.5); }
  .focus\\:ring-neon-blue:focus { box-shadow: 0 0 0 2px rgba(0, 191, 255, 0.5); }

  /* 悬停阴影 */
  .hover\\:shadow-neon-blue\/30:hover { box-shadow: 0 10px 15px -3px rgba(0, 191, 255, 0.3); }

  /* 背景透明度 */
  .bg-dark-blue\/80 { background-color: rgba(11, 14, 47, 0.8); }
  .bg-dark-blue\/95 { background-color: rgba(11, 14, 47, 0.95); }

  /* 其他缺少的样式 */
  .pt-24 { padding-top: 6rem; }
  .pb-16 { padding-bottom: 4rem; }
  .pb-24 { padding-bottom: 6rem; }
  .pt-8 { padding-top: 2rem; }
  .mt-8 { margin-top: 2rem; }
  .mt-12 { margin-top: 3rem; }
  .mb-16 { margin-bottom: 4rem; }
  .mb-5 { margin-bottom: 1.25rem; }
  .mb-1 { margin-bottom: 0.25rem; }
  .ml-1 { margin-left: 0.25rem; }
  .inline-block { display: inline-block; }
  .flex-wrap { flex-wrap: wrap; }
  .max-w-5xl { max-width: 64rem; }
  .lg\\:w-1\/2 { width: 50%; }
  .lg\\:mb-0 { margin-bottom: 0; }
  @media (min-width: 1024px) {
    .lg\\:w-1\/2 { width: 50%; }
    .lg\\:mb-0 { margin-bottom: 0; }
    .lg\\:flex-row { flex-direction: row; }
    .lg\\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  }
</style>
{% endblock %}

{% block content %}
<!-- 主页内容 -->
<div class="min-h-screen bg-gradient-to-b from-primary/5 to-white">
  <!-- 英雄区域 -->
  <section class="relative overflow-hidden bg-gradient-to-br from-dark-blue to-dark pt-24 pb-16 clip-path-slant">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 bg-grid opacity-20"></div>

    <!-- 浮动装饰元素 -->
    <div class="absolute w-60 h-60 bg-neon-blue/20 rounded-full blur-3xl -top-40 -left-40 animate-float"></div>
    <div class="absolute w-80 h-80 bg-neon-purple/20 rounded-full blur-3xl -bottom-40 -right-40 animate-float" style="animation-delay: -3s;"></div>
    <div class="absolute w-64 h-64 bg-neon-green/20 rounded-full blur-3xl top-1/4 -left-32 animate-float" style="animation-delay: -1.5s;"></div>

    <div class="container mx-auto px-6 relative z-10">
      <div class="flex flex-col lg:flex-row items-center justify-between">
        <!-- 左侧内容 -->
        <div class="lg:w-1/2 text-white mb-12 lg:mb-0">
          <h1 class="text-[clamp(2rem,5vw,3.5rem)] font-bold mb-6 leading-tight">
            <span class="neon-text">智慧食堂</span><br>
            <span class="text-neon-blue">数字化管理平台</span>
          </h1>
          <p class="text-xl mb-8 text-gray-300 leading-relaxed">
            基于人工智能和大数据技术，为校园食堂提供全方位的数字化管理解决方案，
            让食堂管理更智能、更高效、更安全。
          </p>
          <div class="flex flex-wrap gap-4">
            <a href="{{ url_for('auth.login') }}" class="bg-neon-blue hover:bg-neon-blue/80 text-white px-8 py-3 rounded-lg font-semibold transition-all duration-300 hover:shadow-neon-blue/30 hover:shadow-lg neon-glow">
              立即体验
            </a>
            <a href="#features" class="border-2 border-neon-blue text-neon-blue hover:bg-neon-blue hover:text-white px-8 py-3 rounded-lg font-semibold transition-all duration-300">
              了解更多
            </a>
          </div>
        </div>

        <!-- 右侧数据可视化 -->
        <div class="lg:w-1/2 relative">
          <div class="bg-dark-blue/80 backdrop-blur-lg rounded-2xl p-8 border border-neon-blue/30 neon-glow">
            <h3 class="text-neon-blue text-xl font-semibold mb-6">实时数据监控</h3>
            <div class="grid grid-cols-2 gap-6">
              <div class="text-center">
                <div class="text-3xl font-bold text-neon-green mb-2 data-pulse">1,247</div>
                <div class="text-gray-400 text-sm">今日用餐人数</div>
              </div>
              <div class="text-center">
                <div class="text-3xl font-bold text-neon-purple mb-2 data-pulse">98.5%</div>
                <div class="text-gray-400 text-sm">食品安全率</div>
              </div>
              <div class="text-center">
                <div class="text-3xl font-bold text-neon-blue mb-2 data-pulse">156</div>
                <div class="text-gray-400 text-sm">菜品种类</div>
              </div>
              <div class="text-center">
                <div class="text-3xl font-bold text-yellow-400 mb-2 data-pulse">4.8</div>
                <div class="text-gray-400 text-sm">用户满意度</div>
              </div>
            </div>

            <!-- 简化的图表区域 -->
            <div class="mt-6 h-32 bg-dark-blue/50 rounded-lg flex items-center justify-center border border-neon-blue/20">
              <canvas id="realtimeChart" width="300" height="120"></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- 核心功能展示 -->
  <section id="features" class="section-padding bg-white">
    <div class="container mx-auto px-6">
      <div class="text-center mb-16">
        <h2 class="text-[clamp(1.8rem,4vw,2.8rem)] font-bold text-dark mb-5">
          🌟 核心功能亮点
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          八大智能化功能，全面保障食堂安全管理
        </p>
      </div>

      <!-- 主要功能卡片 -->
      <div class="grid md:grid-cols-3 gap-8 mb-16">
        <div class="bg-white rounded-2xl p-8 shadow-lg card-hover border border-gray-100">
          <div class="text-center mb-6">
            <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <i class="fas fa-qrcode text-primary text-2xl"></i>
            </div>
            <h3 class="text-xl font-semibold text-dark mb-3">智能检查系统</h3>
            <p class="text-gray-600 mb-4">员工扫码上传食堂卫生、设备情况，管理员在线评价，实时监控运营状态</p>
            <div class="flex justify-center gap-2">
              <span class="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm">扫码上传</span>
              <span class="px-3 py-1 bg-green-100 text-green-600 rounded-full text-sm">在线评价</span>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-2xl p-8 shadow-lg card-hover border border-gray-100">
          <div class="text-center mb-6">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <i class="fas fa-users text-green-600 text-2xl"></i>
            </div>
            <h3 class="text-xl font-semibold text-dark mb-3">家校共陪餐</h3>
            <p class="text-gray-600 mb-4">家长参与陪餐体验，透明化食堂管理，增强家校信任</p>
            <div class="flex justify-center gap-2">
              <span class="px-3 py-1 bg-green-100 text-green-600 rounded-full text-sm">透明管理</span>
              <span class="px-3 py-1 bg-blue-100 text-blue-600 rounded-full text-sm">家校互动</span>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-2xl p-8 shadow-lg card-hover border border-gray-100">
          <div class="text-center mb-6">
            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <i class="fas fa-file-alt text-blue-600 text-2xl"></i>
            </div>
            <h3 class="text-xl font-semibold text-dark mb-3">智能日志生成</h3>
            <p class="text-gray-600 mb-4">每天自动生成工作日志，全面记录食堂运营情况，数据化管理决策</p>
            <div class="flex justify-center gap-2">
              <span class="px-3 py-1 bg-blue-100 text-blue-600 rounded-full text-sm">自动生成</span>
              <span class="px-3 py-1 bg-yellow-100 text-yellow-600 rounded-full text-sm">数据分析</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 其他核心功能 -->
      <div class="grid md:grid-cols-4 gap-6 mb-12">
        <div class="text-center p-6 bg-gray-50 rounded-xl card-hover">
          <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-calendar-alt text-yellow-600"></i>
          </div>
          <h4 class="font-semibold text-dark mb-2">灵活菜单管理</h4>
          <p class="text-gray-600 text-sm">周菜单灵活安排，支持打印，一键导入采购计划</p>
        </div>

        <div class="text-center p-6 bg-gray-50 rounded-xl card-hover">
          <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-shopping-cart text-red-600"></i>
          </div>
          <h4 class="font-semibold text-dark mb-2">智能采购系统</h4>
          <p class="text-gray-600 text-sm">灵活选择供应商，智能价格对比，自动生成采购单</p>
        </div>

        <div class="text-center p-6 bg-gray-50 rounded-xl card-hover">
          <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-warehouse text-purple-600"></i>
          </div>
          <h4 class="font-semibold text-dark mb-2">出入库管理</h4>
          <p class="text-gray-600 text-sm">完整流程管理，自动生成台账报表，库存实时监控</p>
        </div>

        <div class="text-center p-6 bg-gray-50 rounded-xl card-hover">
          <div class="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-search text-gray-600"></i>
          </div>
          <h4 class="font-semibold text-dark mb-2">全程溯源</h4>
          <p class="text-gray-600 text-sm">食品全程可追溯，供应链透明化，安全责任到人</p>
        </div>
      </div>

      <!-- 特色功能亮点 -->
      <div class="grid md:grid-cols-2 gap-8">
        <div class="text-center p-8 bg-primary/5 rounded-xl card-hover">
          <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-print text-primary text-2xl"></i>
          </div>
          <h4 class="text-xl font-semibold text-dark mb-3">一键式留样标签打印</h4>
          <p class="text-gray-600">规范化留样流程，自动生成标签，确保食品安全</p>
        </div>

        <div class="text-center p-8 bg-green-50 rounded-xl card-hover">
          <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-chart-line text-green-600 text-2xl"></i>
          </div>
          <h4 class="text-xl font-semibold text-dark mb-3">数据分析决策</h4>
          <p class="text-gray-600">智能数据分析，帮助管理者做出科学决策</p>
        </div>
      </div>
    </div>
  </section>

  <!-- 统计数据展示 -->
  <section class="section-padding bg-gray-50">
    <div class="container mx-auto px-6">
      <div class="grid md:grid-cols-4 gap-8">
        <div class="text-center p-8 bg-white rounded-2xl shadow-lg card-hover">
          <div class="text-4xl font-bold text-neon-blue mb-2" id="stat-safe">100</div>
          <div class="text-gray-600">食品安全追溯</div>
          <div class="text-xs text-gray-400 mt-1">%</div>
        </div>
        <div class="text-center p-8 bg-white rounded-2xl shadow-lg card-hover">
          <div class="text-4xl font-bold text-neon-green mb-2" id="stat-monitor">24</div>
          <div class="text-gray-600">实时监控</div>
          <div class="text-xs text-gray-400 mt-1">小时</div>
        </div>
        <div class="text-center p-8 bg-white rounded-2xl shadow-lg card-hover">
          <div class="text-4xl font-bold text-neon-purple mb-2" id="stat-intel">100</div>
          <div class="text-gray-600">管理流程</div>
          <div class="text-xs text-gray-400 mt-1">%</div>
        </div>
        <div class="text-center p-8 bg-white rounded-2xl shadow-lg card-hover">
          <div class="text-4xl font-bold text-yellow-500 mb-2" id="stat-trans">100</div>
          <div class="text-gray-600">家校共管</div>
          <div class="text-xs text-gray-400 mt-1">%</div>
        </div>
      </div>
    </div>
  </section>

  <!-- 行动号召 -->
  <section class="section-padding bg-gradient-to-br from-dark-blue to-dark text-white relative overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute w-80 h-80 bg-neon-blue/10 rounded-full blur-3xl -top-40 -right-40 animate-float"></div>
    <div class="absolute w-64 h-64 bg-neon-purple/10 rounded-full blur-3xl -bottom-40 -left-40 animate-float" style="animation-delay: -2s;"></div>

    <div class="container mx-auto px-6 text-center relative z-10">
      <h2 class="text-[clamp(1.8rem,4vw,2.8rem)] font-bold mb-6">
        开始您的智慧食堂管理之旅
      </h2>
      <p class="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
        让食品安全管理更简单、更智能、更透明
      </p>
      <div class="flex flex-wrap justify-center gap-4">
        <a href="{{ url_for('auth.login') }}" class="bg-neon-blue hover:bg-neon-blue/80 text-white px-8 py-4 rounded-lg font-semibold transition-all duration-300 hover:shadow-neon-blue/30 hover:shadow-lg neon-glow inline-flex items-center">
          <i class="fas fa-rocket mr-2"></i>立即开始
        </a>
        <a href="{{ url_for('auth.register') }}" class="border-2 border-neon-blue text-neon-blue hover:bg-neon-blue hover:text-white px-8 py-4 rounded-lg font-semibold transition-all duration-300 inline-flex items-center">
          <i class="fas fa-user-plus mr-2"></i>注册账号
        </a>
      </div>
    </div>
  </section>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 初始化实时图表
    initRealtimeChart();

    // 统计数字动画
    animateCounters();

    // 平滑滚动
    initSmoothScroll();

    // 卡片悬停效果
    initCardHoverEffects();
});

// 初始化实时图表
function initRealtimeChart() {
    const canvas = document.getElementById('realtimeChart');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    // 简单的实时数据模拟
    const data = {
        labels: ['09:00', '10:00', '11:00', '12:00', '13:00', '14:00'],
        datasets: [{
            label: '用餐人数',
            data: [120, 180, 250, 420, 380, 200],
            borderColor: '#00BFFF',
            backgroundColor: 'rgba(0, 191, 255, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
        }]
    };

    new Chart(ctx, {
        type: 'line',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                x: {
                    display: true,
                    grid: {
                        color: 'rgba(0, 191, 255, 0.1)'
                    },
                    ticks: {
                        color: '#9CA3AF',
                        font: {
                            size: 10
                        }
                    }
                },
                y: {
                    display: true,
                    grid: {
                        color: 'rgba(0, 191, 255, 0.1)'
                    },
                    ticks: {
                        color: '#9CA3AF',
                        font: {
                            size: 10
                        }
                    }
                }
            },
            elements: {
                point: {
                    radius: 3,
                    hoverRadius: 5
                }
            }
        }
    });
}

// 统计数字动画
function animateCounters() {
    const counters = [
        { id: 'stat-safe', target: 100, suffix: '' },
        { id: 'stat-monitor', target: 24, suffix: '' },
        { id: 'stat-intel', target: 100, suffix: '' },
        { id: 'stat-trans', target: 100, suffix: '' }
    ];

    counters.forEach(counter => {
        animateCount(counter.id, counter.target, counter.suffix);
    });
}

function animateCount(id, target, suffix = '', duration = 2000) {
    const element = document.getElementById(id);
    if (!element) return;

    let start = 0;
    const increment = target / (duration / 16);

    function updateCount() {
        start += increment;
        if (start >= target) {
            element.textContent = target + suffix;
        } else {
            element.textContent = Math.floor(start) + suffix;
            requestAnimationFrame(updateCount);
        }
    }

    updateCount();
}

// 平滑滚动
function initSmoothScroll() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// 卡片悬停效果
function initCardHoverEffects() {
    const cards = document.querySelectorAll('.card-hover');

    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
}

// 数据脉冲动画
setInterval(() => {
    const pulseElements = document.querySelectorAll('.data-pulse');
    pulseElements.forEach(el => {
        el.style.transform = 'scale(1.05)';
        setTimeout(() => {
            el.style.transform = 'scale(1)';
        }, 200);
    });
}, 3000);

// 滚动时的视差效果
window.addEventListener('scroll', () => {
    const scrolled = window.pageYOffset;
    const parallax = document.querySelectorAll('.animate-float');

    parallax.forEach(element => {
        const speed = 0.5;
        element.style.transform = `translateY(${scrolled * speed}px)`;
    });
});
</script>
{% endblock %}
