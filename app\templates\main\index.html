<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>校园餐智慧食堂平台 - 智能化食堂管理解决方案</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <link href="{{ url_for('static', filename='css/font-awesome.min.css') }}" rel="stylesheet">
  <script src="{{ url_for('static', filename='js/chart.umd.min.js') }}"></script>

  <!-- 配置Tailwind CSS -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#165DFF',
            secondary: '#36CBCB',
            accent: '#722ED1',
            dark: '#1D2129',
            light: '#F7F8FA',
            'primary-light': '#E8F3FF',
            'primary-dark': '#0D47A1',
            'neon-blue': '#00BFFF',
            'neon-purple': '#9D4EDD',
            'neon-green': '#00FF9D',
            'dark-blue': '#0B0E2F',
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
            code: ['JetBrains Mono', 'monospace'],
          },
        },
      }
    }
  </script>

  <style>
    /* 自定义样式 */
    .content-auto {
      content-visibility: auto;
    }
    .text-shadow {
      text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .card-hover {
      transition: all 0.3s ease;
    }
    .card-hover:hover {
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
      transform: translateY(-0.25rem);
    }
    .section-padding {
      padding-top: 4rem;
      padding-bottom: 4rem;
    }
    @media (min-width: 768px) {
      .section-padding {
        padding-top: 6rem;
        padding-bottom: 6rem;
      }
    }
    .bg-gradient-primary {
      background: linear-gradient(to right, #165DFF, #0D47A1);
    }
    .animate-float {
      animation: float 6s ease-in-out infinite;
    }
    .neon-glow {
      box-shadow: 0 0 10px rgba(0, 191, 255, 0.5), 0 0 20px rgba(0, 191, 255, 0.3);
    }
    .neon-text {
      text-shadow: 0 0 10px rgba(0, 191, 255, 0.7);
    }
    .data-pulse {
      animation: pulse 2s infinite;
    }
    @keyframes float {
      0% { transform: translateY(0px); }
      50% { transform: translateY(-20px); }
      100% { transform: translateY(0px); }
    }
    @keyframes pulse {
      0% { transform: scale(1); opacity: 1; }
      50% { transform: scale(1.05); opacity: 0.8; }
      100% { transform: scale(1); opacity: 1; }
    }
    .bg-grid {
      background-image: linear-gradient(rgba(22, 93, 255, 0.05) 1px, transparent 1px),
                        linear-gradient(90deg, rgba(22, 93, 255, 0.05) 1px, transparent 1px);
      background-size: 20px 20px;
    }
    .clip-path-slant {
      clip-path: polygon(0 0, 100% 0, 100% 85%, 0 100%);
    }

    /* 缺少的样式 */
    .text-xs { font-size: 0.75rem; }
    .w-2 { width: 0.5rem; }
    .h-2 { height: 0.5rem; }
    .w-60 { width: 15rem; }
    .h-60 { height: 15rem; }
    .w-64 { width: 16rem; }
    .h-64 { height: 16rem; }
    .w-80 { width: 20rem; }
    .h-80 { height: 20rem; }
    .-top-40 { top: -10rem; }
    .-left-40 { left: -10rem; }
    .-bottom-40 { bottom: -10rem; }
    .-right-40 { right: -10rem; }
    .-left-20 { left: -5rem; }
    .-right-20 { right: -5rem; }
    .-left-32 { left: -8rem; }
    .-right-32 { right: -8rem; }
    .top-1\/4 { top: 25%; }
    .bottom-1\/4 { bottom: 25%; }
    .top-1\/3 { top: 33.333333%; }
    .bottom-1\/3 { bottom: 33.333333%; }
    .left-1\/2 { left: 50%; }
    .transform { transform: translateZ(0); }
    .-translate-x-1\/2 { transform: translateX(-50%); }
    .blur-3xl { filter: blur(64px); }
    .opacity-50 { opacity: 0.5; }

    /* 响应式文本大小 */
    .text-\[clamp\(2rem\,5vw\,3\.5rem\)\] { font-size: clamp(2rem, 5vw, 3.5rem); }
    .text-\[clamp\(1\.8rem\,4vw\,2\.8rem\)\] { font-size: clamp(1.8rem, 4vw, 2.8rem); }

    /* 间距 */
    .gap-4 { gap: 1rem; }
    .space-y-12 > * + * { margin-top: 3rem; }
    .space-y-6 > * + * { margin-top: 1.5rem; }

    /* 边框和颜色 */
    .border-neon-blue\/30 { border-color: rgba(0, 191, 255, 0.3); }
    .border-neon-blue\/50 { border-color: rgba(0, 191, 255, 0.5); }
    .bg-neon-blue\/20 { background-color: rgba(0, 191, 255, 0.2); }
    .bg-neon-purple\/20 { background-color: rgba(157, 78, 221, 0.2); }
    .bg-neon-green\/20 { background-color: rgba(0, 255, 157, 0.2); }
    .bg-neon-blue\/10 { background-color: rgba(0, 191, 255, 0.1); }
    .bg-neon-purple\/10 { background-color: rgba(157, 78, 221, 0.1); }

    /* 渐变 */
    .bg-gradient-to-b { background: linear-gradient(to bottom, var(--tw-gradient-stops)); }
    .from-primary\/5 { --tw-gradient-from: rgba(22, 93, 255, 0.05); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(22, 93, 255, 0)); }
    .from-neon-blue { --tw-gradient-from: #00BFFF; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(0, 191, 255, 0)); }
    .to-neon-purple { --tw-gradient-to: #9D4EDD; }
    .from-dark-blue { --tw-gradient-from: #0B0E2F; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(11, 14, 47, 0)); }
    .to-dark { --tw-gradient-to: #1D2129; }

    /* 其他 */
    .order-1 { order: 1; }
    .order-2 { order: 2; }
    .order-3 { order: 3; }
    .md\\:order-2 { order: 2; }
    .md\\:order-3 { order: 3; }
    @media (min-width: 768px) {
      .md\\:order-2 { order: 2; }
      .md\\:order-3 { order: 3; }
      .md\\:text-right { text-align: right; }
      .md\\:pr-12 { padding-right: 3rem; }
      .md\\:pl-12 { padding-left: 3rem; }
      .md\\:p-12 { padding: 3rem; }
    }

    /* 焦点样式 */
    .focus\\:ring-2:focus { box-shadow: 0 0 0 2px rgba(0, 191, 255, 0.5); }
    .focus\\:ring-neon-blue:focus { box-shadow: 0 0 0 2px rgba(0, 191, 255, 0.5); }

    /* 悬停阴影 */
    .hover\\:shadow-neon-blue\/30:hover { box-shadow: 0 10px 15px -3px rgba(0, 191, 255, 0.3); }

    /* 背景透明度 */
    .bg-dark-blue\/80 { background-color: rgba(11, 14, 47, 0.8); }
    .bg-dark-blue\/95 { background-color: rgba(11, 14, 47, 0.95); }

    /* 其他缺少的样式 */
    .pt-24 { padding-top: 6rem; }
    .pb-16 { padding-bottom: 4rem; }
    .pb-24 { padding-bottom: 6rem; }
    .pt-8 { padding-top: 2rem; }
    .mt-8 { margin-top: 2rem; }
    .mt-12 { margin-top: 3rem; }
    .mb-16 { margin-bottom: 4rem; }
    .mb-5 { margin-bottom: 1.25rem; }
    .mb-1 { margin-bottom: 0.25rem; }
    .ml-1 { margin-left: 0.25rem; }
    .inline-block { display: inline-block; }
    .flex-wrap { flex-wrap: wrap; }
    .max-w-5xl { max-width: 64rem; }
    .lg\\:w-1\/2 { width: 50%; }
    .lg\\:mb-0 { margin-bottom: 0; }
    @media (min-width: 1024px) {
      .lg\\:w-1\/2 { width: 50%; }
      .lg\\:mb-0 { margin-bottom: 0; }
      .lg\\:flex-row { flex-direction: row; }
      .lg\\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
    }
  </style>
</head>

<body class="font-inter bg-dark-blue text-white antialiased overflow-x-hidden">
  <!-- 背景网格 -->
  <div class="fixed inset-0 bg-grid z-0 opacity-50"></div>

  <!-- 导航栏 -->
  <header id="navbar" class="fixed w-full top-0 z-50 transition-all duration-300 bg-dark-blue/80 backdrop-blur-md border-b border-primary/20">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16 md:h-20">
        <div class="flex items-center">
          <a href="#" class="flex items-center space-x-2">
            <div class="w-10 h-10 bg-primary rounded-lg flex items-center justify-center neon-glow">
              <i class="fas fa-utensils text-white text-xl"></i>
            </div>
            <span class="text-xl font-bold text-white">智慧食堂<span class="text-neon-blue neon-text">平台</span></span>
          </a>
        </div>

        <!-- 桌面导航 -->
        <nav class="hidden md:flex space-x-8">
          <a href="#features" class="text-white hover:text-neon-blue transition-colors font-medium">核心功能</a>
          <a href="#advantages" class="text-white hover:text-neon-blue transition-colors font-medium">系统优势</a>
          <a href="#process" class="text-white hover:text-neon-blue transition-colors font-medium">管理流程</a>
          <a href="#contact" class="text-white hover:text-neon-blue transition-colors font-medium">联系我们</a>
        </nav>

        <!-- 登录按钮 -->
        <div class="hidden md:flex space-x-4">
          <a href="{{ url_for('auth.login') }}" class="bg-gradient-to-r from-primary to-neon-blue text-white font-medium px-6 py-2 rounded-lg transition-all shadow-lg hover:shadow-neon-blue/30 neon-glow">
            登录系统
          </a>
        </div>

        <!-- 移动端菜单按钮 -->
        <div class="md:hidden">
          <button id="menu-toggle" class="text-white hover:text-neon-blue focus:outline-none">
            <i class="fas fa-bars text-2xl"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- 移动端导航菜单 -->
    <div id="mobile-menu" class="md:hidden hidden bg-dark-blue/95 backdrop-blur-md border-t border-primary/20">
      <div class="container mx-auto px-4 py-3 space-y-3">
        <a href="#features" class="block text-white hover:text-neon-blue transition-colors font-medium py-2">核心功能</a>
        <a href="#advantages" class="block text-white hover:text-neon-blue transition-colors font-medium py-2">系统优势</a>
        <a href="#process" class="block text-white hover:text-neon-blue transition-colors font-medium py-2">管理流程</a>
        <a href="#contact" class="block text-white hover:text-neon-blue transition-colors font-medium py-2">联系我们</a>
        <a href="{{ url_for('auth.login') }}" class="block bg-gradient-to-r from-primary to-neon-blue text-white font-medium px-6 py-2 rounded-lg transition-all shadow-lg hover:shadow-neon-blue/30 neon-glow text-center">
          登录系统
        </a>
      </div>
    </div>
  </header>

  <!-- 英雄区域 -->
  <section class="pt-24 md:pt-32 pb-16 md:pb-24 bg-gradient-to-b from-primary/5 to-dark-blue relative overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute -top-40 -left-40 w-80 h-80 bg-neon-blue/20 rounded-full blur-3xl"></div>
    <div class="absolute -bottom-40 -right-40 w-80 h-80 bg-neon-purple/20 rounded-full blur-3xl"></div>

    <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
      <div class="flex flex-col lg:flex-row items-center">
        <div class="lg:w-1/2 mb-10 lg:mb-0">
          <h1 class="text-[clamp(2rem,5vw,3.5rem)] font-bold leading-tight text-white mb-6">
            智慧食堂管理平台<br>
            <span class="text-neon-blue neon-text">全方位智能化解决方案</span>
          </h1>
          <p class="text-lg md:text-xl text-gray-300 mb-8 max-w-xl">
            致力于打造全方位智能化管理体系，实现食品安全可视化、可管控、可追溯，为校园食堂管理提供高效便捷的技术支持。
          </p>
          <div class="flex flex-wrap gap-4">
            <a href="#features" class="bg-gradient-to-r from-primary to-neon-blue text-white font-medium px-8 py-3 rounded-lg transition-all shadow-lg hover:shadow-neon-blue/30 flex items-center neon-glow">
              了解核心功能
              <i class="fas fa-arrow-right ml-2"></i>
            </a>
            <a href="#contact" class="bg-transparent hover:bg-white/10 text-white border border-neon-blue/50 font-medium px-8 py-3 rounded-lg transition-all flex items-center">
              联系我们
              <i class="fas fa-envelope ml-2"></i>
            </a>
          </div>

          <!-- 数据指标 -->
          <div class="mt-12 grid grid-cols-3 gap-4">
            <div class="bg-dark/30 backdrop-blur-sm p-4 rounded-lg border border-primary/20">
              <p class="text-neon-blue font-code text-2xl font-bold">99.9%</p>
              <p class="text-sm text-gray-400">系统稳定性</p>
            </div>
            <div class="bg-dark/30 backdrop-blur-sm p-4 rounded-lg border border-primary/20">
              <p class="text-neon-green font-code text-2xl font-bold">80%</p>
              <p class="text-sm text-gray-400">管理效率提升</p>
            </div>
            <div class="bg-dark/30 backdrop-blur-sm p-4 rounded-lg border border-primary/20">
              <p class="text-neon-purple font-code text-2xl font-bold">100%</p>
              <p class="text-sm text-gray-400">食品溯源率</p>
            </div>
          </div>
        </div>
        <div class="lg:w-1/2 relative">
          <div class="relative z-10 animate-float">
            <div class="bg-dark/50 backdrop-blur-md rounded-2xl shadow-2xl border border-primary/30 overflow-hidden">
              <img src="{{ url_for('static', filename='images/dashboard.jpg') }}" alt="智慧食堂管理系统界面" class="w-full h-auto">
              <div class="p-4 border-t border-primary/20">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-neon-green/20 rounded-full flex items-center justify-center text-neon-green">
                      <i class="fas fa-check text-lg"></i>
                    </div>
                    <div>
                      <p class="text-sm font-medium text-white">食品安全</p>
                      <p class="text-xs text-gray-400">全程可追溯</p>
                    </div>
                  </div>
                  <div class="text-xs text-gray-400">实时监控中 <span class="inline-block w-2 h-2 bg-neon-green rounded-full animate-pulse ml-1"></span></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- 数据仪表盘 -->
  <section class="py-12 bg-dark/50 relative">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <div class="bg-dark/50 backdrop-blur-md rounded-xl border border-primary/20 p-6 shadow-lg">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
          <h3 class="text-xl font-bold text-white mb-4 md:mb-0">系统运行数据 <span class="text-neon-blue text-sm font-normal">实时更新</span></h3>
          <div class="flex space-x-3">
            <button class="px-3 py-1 bg-primary/20 text-white text-sm rounded hover:bg-primary/30 transition-colors">今日</button>
            <button class="px-3 py-1 bg-primary/10 text-gray-400 text-sm rounded hover:bg-primary/30 transition-colors">本周</button>
            <button class="px-3 py-1 bg-primary/10 text-gray-400 text-sm rounded hover:bg-primary/30 transition-colors">本月</button>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- 图表1 -->
          <div class="bg-dark/30 rounded-lg p-4 border border-primary/20">
            <h4 class="text-white text-sm font-medium mb-3">食堂运营效率</h4>
            <div class="h-48">
              <canvas id="efficiencyChart"></canvas>
            </div>
          </div>

          <!-- 图表2 -->
          <div class="bg-dark/30 rounded-lg p-4 border border-primary/20">
            <h4 class="text-white text-sm font-medium mb-3">食品安全检测</h4>
            <div class="h-48">
              <canvas id="safetyChart"></canvas>
            </div>
          </div>

          <!-- 图表3 -->
          <div class="bg-dark/30 rounded-lg p-4 border border-primary/20">
            <h4 class="text-white text-sm font-medium mb-3">用户满意度</h4>
            <div class="h-48">
              <canvas id="satisfactionChart"></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- 核心功能 -->
  <section id="features" class="section-padding bg-dark-blue relative overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute top-1/4 -left-20 w-60 h-60 bg-neon-purple/10 rounded-full blur-3xl"></div>
    <div class="absolute bottom-1/4 -right-20 w-60 h-60 bg-neon-blue/10 rounded-full blur-3xl"></div>

    <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
      <div class="text-center max-w-3xl mx-auto mb-16">
        <h2 class="text-[clamp(1.8rem,4vw,2.8rem)] font-bold text-white mb-4">八大智能化功能，全面保障食堂安全管理</h2>
        <p class="text-lg text-gray-300">
          我们的智慧食堂平台集成了多项先进功能，从食材采购到餐后服务，全方位提升食堂管理效率与食品安全
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        <!-- 功能卡片1 -->
        <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 group">
          <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center mb-5 group-hover:bg-primary/30 transition-colors">
            <i class="fas fa-search text-neon-blue text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">智能检查系统</h3>
          <p class="text-gray-400">
            员工通过扫码上传食堂卫生状况、设备运行情况，管理员在线进行评价反馈，实时监控食堂运营状态
          </p>
        </div>

        <!-- 功能卡片2 -->
        <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 group">
          <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center mb-5 group-hover:bg-primary/30 transition-colors">
            <i class="fas fa-users text-neon-blue text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">家校共陪餐</h3>
          <p class="text-gray-400">
            邀请家长参与陪餐体验，提升食堂管理透明度，加强家校互动沟通，增强家长对食堂的信任度
          </p>
        </div>

        <!-- 功能卡片3 -->
        <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 group">
          <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center mb-5 group-hover:bg-primary/30 transition-colors">
            <i class="far fa-file-alt text-neon-blue text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">智能日志生成</h3>
          <p class="text-gray-400">
            每日自动生成食堂工作日志，完整记录运营情况，基于数据进行分析，为管理决策提供依据
          </p>
        </div>

        <!-- 功能卡片4 -->
        <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 group">
          <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center mb-5 group-hover:bg-primary/30 transition-colors">
            <i class="fas fa-list text-neon-blue text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">灵活菜单管理</h3>
          <p class="text-gray-400">
            支持周菜单灵活安排与调整，可直接打印输出，一键导入菜单信息生成采购计划，提高工作效率
          </p>
        </div>

        <!-- 功能卡片5 -->
        <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 group">
          <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center mb-5 group-hover:bg-primary/30 transition-colors">
            <i class="fas fa-shopping-cart text-neon-blue text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">智能采购系统</h3>
          <p class="text-gray-400">
            提供供应商灵活选择功能，支持智能价格对比，依据采购需求自动生成采购单，简化采购流程
          </p>
        </div>

        <!-- 功能卡片6 -->
        <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 group">
          <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center mb-5 group-hover:bg-primary/30 transition-colors">
            <i class="fas fa-archive text-neon-blue text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">出入库管理</h3>
          <p class="text-gray-400">
            对出入库流程进行完整管理，自动生成台账报表，实时监控库存情况，确保库存管理精准高效
          </p>
        </div>

        <!-- 功能卡片7 -->
        <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 group">
          <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center mb-5 group-hover:bg-primary/30 transition-colors">
            <i class="fas fa-search-minus text-neon-blue text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">全程溯源</h3>
          <p class="text-gray-400">
            实现食品从源头到餐桌的全程可追溯，打造透明化供应链，明确安全责任到人
          </p>
        </div>

        <!-- 功能卡片8 -->
        <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 group">
          <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center mb-5 group-hover:bg-primary/30 transition-colors">
            <i class="fas fa-barcode text-neon-blue text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">一键式留样标签打印</h3>
          <p class="text-gray-400">
            规范食品留样流程，自动生成留样标签，确保食品安全管理符合规范要求
          </p>
        </div>
      </div>
    </div>
  </section>

  <!-- 联系我们 -->
  <section id="contact" class="section-padding bg-gradient-to-b from-dark-blue to-dark relative overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute top-1/3 -left-32 w-64 h-64 bg-neon-green/10 rounded-full blur-3xl"></div>
    <div class="absolute bottom-1/3 -right-32 w-64 h-64 bg-neon-purple/10 rounded-full blur-3xl"></div>

    <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
      <div class="text-center max-w-3xl mx-auto mb-16">
        <h2 class="text-[clamp(1.8rem,4vw,2.8rem)] font-bold text-white mb-4">开始您的智慧食堂管理之旅</h2>
        <p class="text-lg text-gray-300">
          让食品安全管理更简单、更智能、更透明。立即体验我们的智慧食堂管理平台
        </p>
      </div>

      <div class="flex flex-col lg:flex-row justify-center items-center gap-8">
        <a href="{{ url_for('auth.login') }}" class="bg-gradient-to-r from-primary to-neon-blue text-white font-medium px-12 py-4 rounded-lg transition-all shadow-lg hover:shadow-neon-blue/30 flex items-center text-lg neon-glow">
          <i class="fas fa-rocket mr-3"></i>
          立即体验系统
        </a>
        <a href="{{ url_for('auth.register') }}" class="bg-transparent hover:bg-white/10 text-white border border-neon-blue/50 font-medium px-12 py-4 rounded-lg transition-all flex items-center text-lg">
          <i class="fas fa-user-plus mr-3"></i>
          注册新账号
        </a>
      </div>

      <!-- 联系信息 -->
      <div class="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
        <div class="text-center">
          <div class="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-phone text-neon-blue text-2xl"></i>
          </div>
          <h3 class="text-white font-semibold mb-2">技术支持</h3>
          <p class="text-gray-400">************</p>
        </div>
        <div class="text-center">
          <div class="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-envelope text-neon-blue text-2xl"></i>
          </div>
          <h3 class="text-white font-semibold mb-2">邮箱联系</h3>
          <p class="text-gray-400"><EMAIL></p>
        </div>
        <div class="text-center">
          <div class="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-clock text-neon-blue text-2xl"></i>
          </div>
          <h3 class="text-white font-semibold mb-2">服务时间</h3>
          <p class="text-gray-400">7×24小时在线服务</p>
        </div>
      </div>
    </div>
  </section>

  <!-- 页脚 -->
  <footer class="bg-dark py-8 border-t border-primary/20">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center">
        <div class="flex items-center justify-center space-x-2 mb-4">
          <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
            <i class="fas fa-utensils text-white"></i>
          </div>
          <span class="text-xl font-bold text-white">智慧食堂<span class="text-neon-blue">平台</span></span>
        </div>
        <p class="text-gray-400 mb-4">
          © 2024 智慧食堂管理平台. 保留所有权利.
        </p>
        <div class="flex justify-center space-x-6">
          <a href="#" class="text-gray-400 hover:text-neon-blue transition-colors">隐私政策</a>
          <a href="#" class="text-gray-400 hover:text-neon-blue transition-colors">服务条款</a>
          <a href="#" class="text-gray-400 hover:text-neon-blue transition-colors">帮助中心</a>
        </div>
      </div>
    </div>
  </footer>

  <!-- JavaScript -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // 移动端菜单切换
      const menuToggle = document.getElementById('menu-toggle');
      const mobileMenu = document.getElementById('mobile-menu');

      menuToggle.addEventListener('click', function() {
        mobileMenu.classList.toggle('hidden');
      });

      // 导航栏滚动效果
      window.addEventListener('scroll', function() {
        const navbar = document.getElementById('navbar');
        if (window.scrollY > 50) {
          navbar.classList.add('bg-dark-blue/95');
          navbar.classList.remove('bg-dark-blue/80');
        } else {
          navbar.classList.add('bg-dark-blue/80');
          navbar.classList.remove('bg-dark-blue/95');
        }
      });

      // 平滑滚动
      document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
          e.preventDefault();
          const target = document.querySelector(this.getAttribute('href'));
          if (target) {
            target.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
          }
        });
      });

      // 初始化图表
      initCharts();
    });

    // 图表初始化
    function initCharts() {
      // 运营效率图表
      const efficiencyCtx = document.getElementById('efficiencyChart');
      if (efficiencyCtx) {
        new Chart(efficiencyCtx, {
          type: 'line',
          data: {
            labels: ['周一', '周二', '周三', '周四', '周五'],
            datasets: [{
              label: '效率指数',
              data: [85, 92, 88, 95, 90],
              borderColor: '#00BFFF',
              backgroundColor: 'rgba(0, 191, 255, 0.1)',
              borderWidth: 2,
              fill: true,
              tension: 0.4
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                display: false
              }
            },
            scales: {
              x: {
                grid: {
                  color: 'rgba(255, 255, 255, 0.1)'
                },
                ticks: {
                  color: '#9CA3AF',
                  font: {
                    size: 10
                  }
                }
              },
              y: {
                grid: {
                  color: 'rgba(255, 255, 255, 0.1)'
                },
                ticks: {
                  color: '#9CA3AF',
                  font: {
                    size: 10
                  }
                }
              }
            }
          }
        });
      }

      // 安全检测图表
      const safetyCtx = document.getElementById('safetyChart');
      if (safetyCtx) {
        new Chart(safetyCtx, {
          type: 'doughnut',
          data: {
            labels: ['合格', '优秀', '待改进'],
            datasets: [{
              data: [70, 25, 5],
              backgroundColor: ['#00FF9D', '#00BFFF', '#9D4EDD'],
              borderWidth: 0
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                display: false
              }
            }
          }
        });
      }

      // 满意度图表
      const satisfactionCtx = document.getElementById('satisfactionChart');
      if (satisfactionCtx) {
        new Chart(satisfactionCtx, {
          type: 'bar',
          data: {
            labels: ['本周', '上周', '上月'],
            datasets: [{
              label: '满意度',
              data: [4.8, 4.6, 4.5],
              backgroundColor: ['#9D4EDD', '#00BFFF', '#00FF9D'],
              borderWidth: 0
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                display: false
              }
            },
            scales: {
              x: {
                grid: {
                  display: false
                },
                ticks: {
                  color: '#9CA3AF',
                  font: {
                    size: 10
                  }
                }
              },
              y: {
                grid: {
                  color: 'rgba(255, 255, 255, 0.1)'
                },
                ticks: {
                  color: '#9CA3AF',
                  font: {
                    size: 10
                  }
                }
              }
            }
          }
        });
      }
    }
  </script>
</body>
</html>
