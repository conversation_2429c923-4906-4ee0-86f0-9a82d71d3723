<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>校园餐智慧食堂平台 - 智能化食堂管理解决方案</title>

  <!-- 本地样式 -->
  <link href="{{ url_for('static', filename='css/tailwind-custom.css') }}" rel="stylesheet">

  <!-- 使用内联Tailwind CSS基础样式 -->
  <style>
    /* Tailwind CSS基础样式 */
    *, ::before, ::after { box-sizing: border-box; border-width: 0; border-style: solid; border-color: #e5e7eb; }
    ::before, ::after { --tw-content: ''; }
    html { line-height: 1.5; -webkit-text-size-adjust: 100%; -moz-tab-size: 4; tab-size: 4; font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; font-feature-settings: normal; font-variation-settings: normal; }
    body { margin: 0; line-height: inherit; }
    hr { height: 0; color: inherit; border-top-width: 1px; }
    abbr:where([title]) { text-decoration: underline dotted; }
    h1, h2, h3, h4, h5, h6 { font-size: inherit; font-weight: inherit; }
    a { color: inherit; text-decoration: inherit; }
    b, strong { font-weight: bolder; }
    code, kbd, samp, pre { font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace; font-size: 1em; }
    small { font-size: 80%; }
    sub, sup { font-size: 75%; line-height: 0; position: relative; vertical-align: baseline; }
    sub { bottom: -0.25em; }
    sup { top: -0.5em; }
    table { text-indent: 0; border-color: inherit; border-collapse: collapse; }
    button, input, optgroup, select, textarea { font-family: inherit; font-feature-settings: inherit; font-variation-settings: inherit; font-size: 100%; font-weight: inherit; line-height: inherit; color: inherit; margin: 0; padding: 0; }
    button, select { text-transform: none; }
    button, [type='button'], [type='reset'], [type='submit'] { -webkit-appearance: button; background-color: transparent; background-image: none; }
    :-moz-focusring { outline: auto; }
    :-moz-ui-invalid { box-shadow: none; }
    progress { vertical-align: baseline; }
    ::-webkit-inner-spin-button, ::-webkit-outer-spin-button { height: auto; }
    [type='search'] { -webkit-appearance: textfield; outline-offset: -2px; }
    ::-webkit-search-decoration { -webkit-appearance: none; }
    ::-webkit-file-upload-button { -webkit-appearance: button; font: inherit; }
    summary { display: list-item; }
    blockquote, dl, dd, h1, h2, h3, h4, h5, h6, hr, figure, p, pre { margin: 0; }
    fieldset { margin: 0; padding: 0; }
    legend { padding: 0; }
    ol, ul, menu { list-style: none; margin: 0; padding: 0; }
    dialog { padding: 0; }
    textarea { resize: vertical; }
    input::placeholder, textarea::placeholder { opacity: 1; color: #9ca3af; }
    button, [role="button"] { cursor: pointer; }
    :disabled { cursor: default; }
    img, svg, video, canvas, audio, iframe, embed, object { display: block; vertical-align: middle; }
    img, video { max-width: 100%; height: auto; }
    [hidden] { display: none; }
  </style>

  <!-- Font Awesome -->
  <link href="{{ url_for('static', filename='css/font-awesome.min.css') }}" rel="stylesheet">

  <!-- Chart.js -->
  <script src="{{ url_for('static', filename='js/chart.umd.min.js') }}"></script>

  <style>
    /* 核心样式定义 */
    .bg-dark-blue { background-color: #0B0E2F; }
    .bg-dark { background-color: #1D2129; }
    .bg-primary { background-color: #165DFF; }
    .text-neon-blue { color: #00BFFF; }
    .text-neon-green { color: #00FF9D; }
    .text-neon-purple { color: #9D4EDD; }
    .text-yellow-400 { color: #facc15; }

    /* 特效样式 */
    .neon-glow { box-shadow: 0 0 10px rgba(0, 191, 255, 0.5), 0 0 20px rgba(0, 191, 255, 0.3); }
    .neon-text { text-shadow: 0 0 10px rgba(0, 191, 255, 0.7); }
    .animate-float { animation: float 6s ease-in-out infinite; }
    .data-pulse { animation: pulse 2s infinite; }
    .card-hover { transition: all 0.3s ease; }
    .card-hover:hover { transform: translateY(-0.25rem); }

    /* 动画 */
    @keyframes float {
      0% { transform: translateY(0px); }
      50% { transform: translateY(-20px); }
      100% { transform: translateY(0px); }
    }
    @keyframes pulse {
      0% { transform: scale(1); opacity: 1; }
      50% { transform: scale(1.05); opacity: 0.8; }
      100% { transform: scale(1); opacity: 1; }
    }

    /* 背景效果 */
    .bg-grid {
      background-image: linear-gradient(rgba(22, 93, 255, 0.05) 1px, transparent 1px),
                        linear-gradient(90deg, rgba(22, 93, 255, 0.05) 1px, transparent 1px);
      background-size: 20px 20px;
    }
    .clip-path-slant { clip-path: polygon(0 0, 100% 0, 100% 85%, 0 100%); }

    /* 间距和布局 */
    .section-padding { padding-top: 4rem; padding-bottom: 4rem; }
    @media (min-width: 768px) {
      .section-padding { padding-top: 6rem; padding-bottom: 6rem; }
    }

    /* 补充缺失的Tailwind类 */
    .text-xs { font-size: 0.75rem; }
    .w-2 { width: 0.5rem; }
    .h-2 { height: 0.5rem; }
    .w-60 { width: 15rem; }
    .h-60 { height: 15rem; }
    .w-64 { width: 16rem; }
    .h-64 { height: 16rem; }
    .w-80 { width: 20rem; }
    .h-80 { height: 20rem; }
    .-top-40 { top: -10rem; }
    .-left-40 { left: -10rem; }
    .-bottom-40 { bottom: -10rem; }
    .-right-40 { right: -10rem; }
    .-left-20 { left: -5rem; }
    .-right-20 { right: -5rem; }
    .-left-32 { left: -8rem; }
    .-right-32 { right: -8rem; }
    .top-1\/4 { top: 25%; }
    .bottom-1\/4 { bottom: 25%; }
    .top-1\/3 { top: 33.333333%; }
    .bottom-1\/3 { bottom: 33.333333%; }
    .left-1\/2 { left: 50%; }
    .blur-3xl { filter: blur(64px); }
    .opacity-50 { opacity: 0.5; }
    .text-\[clamp\(2rem\,5vw\,3\.5rem\)\] { font-size: clamp(2rem, 5vw, 3.5rem); }
    .text-\[clamp\(1\.8rem\,4vw\,2\.8rem\)\] { font-size: clamp(1.8rem, 4vw, 2.8rem); }
    .border-neon-blue\/30 { border-color: rgba(0, 191, 255, 0.3); }
    .border-neon-blue\/50 { border-color: rgba(0, 191, 255, 0.5); }
    .bg-neon-blue\/20 { background-color: rgba(0, 191, 255, 0.2); }
    .bg-neon-purple\/20 { background-color: rgba(157, 78, 221, 0.2); }
    .bg-neon-green\/20 { background-color: rgba(0, 255, 157, 0.2); }
    .bg-neon-blue\/10 { background-color: rgba(0, 191, 255, 0.1); }
    .bg-neon-purple\/10 { background-color: rgba(157, 78, 221, 0.1); }
    .bg-neon-green\/10 { background-color: rgba(0, 255, 157, 0.1); }
    .from-primary\/5 { --tw-gradient-from: rgba(22, 93, 255, 0.05); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(22, 93, 255, 0)); }
    .from-dark-blue { --tw-gradient-from: #0B0E2F; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(11, 14, 47, 0)); }
    .to-dark { --tw-gradient-to: #1D2129; }
    .bg-dark-blue\/80 { background-color: rgba(11, 14, 47, 0.8); }
    .bg-dark-blue\/95 { background-color: rgba(11, 14, 47, 0.95); }
    .pt-24 { padding-top: 6rem; }
    .pb-16 { padding-bottom: 4rem; }
    .pb-24 { padding-bottom: 6rem; }
    .pt-8 { padding-top: 2rem; }
    .mt-8 { margin-top: 2rem; }
    .mt-12 { margin-top: 3rem; }
    .mb-16 { margin-bottom: 4rem; }
    .mb-5 { margin-bottom: 1.25rem; }
    .mb-1 { margin-bottom: 0.25rem; }
    .ml-1 { margin-left: 0.25rem; }
    .inline-block { display: inline-block; }
    .flex-wrap { flex-wrap: wrap; }
    .max-w-5xl { max-width: 64rem; }
    .lg\\:w-1\/2 { width: 50%; }
    .lg\\:mb-0 { margin-bottom: 0; }
    @media (min-width: 1024px) {
      .lg\\:w-1\/2 { width: 50%; }
      .lg\\:mb-0 { margin-bottom: 0; }
      .lg\\:flex-row { flex-direction: row; }
      .lg\\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
    }
    .hover\\:shadow-neon-blue\/30:hover { box-shadow: 0 10px 15px -3px rgba(0, 191, 255, 0.3); }
    .text-yellow-400 { color: #facc15; }
    .text-yellow-500 { color: #eab308; }
    .px-12 { padding-left: 3rem; padding-right: 3rem; }
    .py-4 { padding-top: 1rem; padding-bottom: 1rem; }
    .mr-2 { margin-right: 0.5rem; }
    .mr-3 { margin-right: 0.75rem; }
    .border-neon-blue\/50 { border-color: rgba(0, 191, 255, 0.5); }
    .border-2 { border-width: 2px; }
    .w-16 { width: 4rem; }
    .h-16 { height: 4rem; }
    .mx-auto { margin-left: auto; margin-right: auto; }
    .font-semibold { font-weight: 600; }
    .text-gray-600 { color: #4b5563; }
    .text-dark { color: #1D2129; }
    .sm\\:px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
    @media (min-width: 640px) {
      .sm\\:px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
    }

    /* 确保关键特效样式 */
    .bg-grid {
      background-image: linear-gradient(rgba(22, 93, 255, 0.05) 1px, transparent 1px),
                        linear-gradient(90deg, rgba(22, 93, 255, 0.05) 1px, transparent 1px);
      background-size: 20px 20px;
    }

    .neon-glow {
      box-shadow: 0 0 10px rgba(0, 191, 255, 0.5), 0 0 20px rgba(0, 191, 255, 0.3);
    }

    .neon-text {
      text-shadow: 0 0 10px rgba(0, 191, 255, 0.7);
    }

    .animate-float {
      animation: float 6s ease-in-out infinite;
    }

    .data-pulse {
      animation: pulse 2s infinite;
    }

    .card-hover {
      transition: all 0.3s ease;
    }

    .card-hover:hover {
      transform: translateY(-0.25rem);
    }

    .section-padding {
      padding-top: 4rem;
      padding-bottom: 4rem;
    }

    @media (min-width: 768px) {
      .section-padding {
        padding-top: 6rem;
        padding-bottom: 6rem;
      }
    }

  </style>
</head>

<body class="font-inter bg-dark-blue text-white antialiased overflow-x-hidden">
  <!-- 背景网格 -->
  <div class="fixed inset-0 bg-grid z-0 opacity-50"></div>

  <!-- 导航栏 -->
  <header id="navbar" class="fixed w-full top-0 z-50 transition-all duration-300 bg-dark-blue/80 backdrop-blur-md border-b border-primary/20">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16 md:h-20">
        <div class="flex items-center">
          <a href="#" class="flex items-center space-x-2">
            <div class="w-10 h-10 bg-primary rounded-lg flex items-center justify-center neon-glow">
              <i class="fas fa-utensils text-white text-xl"></i>
            </div>
            <span class="text-xl font-bold text-white">智慧食堂<span class="text-neon-blue neon-text">平台</span></span>
          </a>
        </div>

        <!-- 桌面导航 -->
        <nav class="hidden md:flex space-x-8">
          <a href="#features" class="text-white hover:text-neon-blue transition-colors font-medium">核心功能</a>
          <a href="#advantages" class="text-white hover:text-neon-blue transition-colors font-medium">系统优势</a>
          <a href="#process" class="text-white hover:text-neon-blue transition-colors font-medium">管理流程</a>
          <a href="#contact" class="text-white hover:text-neon-blue transition-colors font-medium">联系我们</a>
        </nav>



        <!-- 移动端菜单按钮 -->
        <div class="md:hidden">
          <button id="menu-toggle" class="text-white hover:text-neon-blue focus:outline-none">
            <i class="fas fa-bars text-2xl"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- 移动端导航菜单 -->
    <div id="mobile-menu" class="md:hidden hidden bg-dark-blue/95 backdrop-blur-md border-t border-primary/20">
      <div class="container mx-auto px-4 py-3 space-y-3">
        <a href="#features" class="block text-white hover:text-neon-blue transition-colors font-medium py-2">核心功能</a>
        <a href="#advantages" class="block text-white hover:text-neon-blue transition-colors font-medium py-2">系统优势</a>
        <a href="#process" class="block text-white hover:text-neon-blue transition-colors font-medium py-2">管理流程</a>
        <a href="#contact" class="block text-white hover:text-neon-blue transition-colors font-medium py-2">联系我们</a>
      </div>
    </div>
  </header>

  <!-- 英雄区域 -->
  <section class="pt-24 md:pt-32 pb-16 md:pb-24 bg-gradient-to-b from-primary/5 to-dark-blue relative overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute -top-40 -left-40 w-80 h-80 bg-neon-blue/20 rounded-full blur-3xl"></div>
    <div class="absolute -bottom-40 -right-40 w-80 h-80 bg-neon-purple/20 rounded-full blur-3xl"></div>

    <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
      <div class="flex flex-col lg:flex-row items-center">
        <div class="lg:w-1/2 mb-10 lg:mb-0">
          <h1 class="text-[clamp(2rem,5vw,3.5rem)] font-bold leading-tight text-white mb-6">
            智慧食堂管理平台<br>
            <span class="text-neon-blue neon-text">全方位智能化解决方案</span>
          </h1>
          <p class="text-lg md:text-xl text-gray-300 mb-8 max-w-xl">
            致力于打造全方位智能化管理体系，实现食品安全可视化、可管控、可追溯，为校园食堂管理提供高效便捷的技术支持。
          </p>
          <div class="flex flex-wrap gap-4">
            <a href="#features" class="bg-gradient-to-r from-primary to-neon-blue text-white font-medium px-8 py-3 rounded-lg transition-all shadow-lg hover:shadow-neon-blue/30 flex items-center neon-glow">
              了解核心功能
              <i class="fas fa-arrow-right ml-2"></i>
            </a>
            <a href="#contact" class="bg-transparent hover:bg-white/10 text-white border border-neon-blue/50 font-medium px-8 py-3 rounded-lg transition-all flex items-center">
              联系我们
              <i class="fas fa-envelope ml-2"></i>
            </a>
          </div>

          <!-- 数据指标 -->
          <div class="mt-12 grid grid-cols-3 gap-4">
            <div class="bg-dark/30 backdrop-blur-sm p-4 rounded-lg border border-primary/20">
              <p class="text-neon-blue font-code text-2xl font-bold">99.9%</p>
              <p class="text-sm text-gray-400">系统稳定性</p>
            </div>
            <div class="bg-dark/30 backdrop-blur-sm p-4 rounded-lg border border-primary/20">
              <p class="text-neon-green font-code text-2xl font-bold">80%</p>
              <p class="text-sm text-gray-400">管理效率提升</p>
            </div>
            <div class="bg-dark/30 backdrop-blur-sm p-4 rounded-lg border border-primary/20">
              <p class="text-neon-purple font-code text-2xl font-bold">100%</p>
              <p class="text-sm text-gray-400">食品溯源率</p>
            </div>
          </div>
        </div>
        <div class="lg:w-1/2 relative">
          <div class="relative z-10 animate-float">
            <div class="bg-dark/50 backdrop-blur-md rounded-2xl shadow-2xl border border-primary/30 overflow-hidden">
              <img src="{{ url_for('static', filename='images/dashboard.jpg') }}" alt="智慧食堂管理系统界面" class="w-full h-auto">
              <div class="p-4 border-t border-primary/20">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-neon-green/20 rounded-full flex items-center justify-center text-neon-green">
                      <i class="fas fa-check text-lg"></i>
                    </div>
                    <div>
                      <p class="text-sm font-medium text-white">食品安全</p>
                      <p class="text-xs text-gray-400">全程可追溯</p>
                    </div>
                  </div>
                  <div class="text-xs text-gray-400">实时监控中 <span class="inline-block w-2 h-2 bg-neon-green rounded-full animate-pulse ml-1"></span></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- 数据仪表盘 -->
  <section class="py-12 bg-dark/50 relative">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <div class="bg-dark/50 backdrop-blur-md rounded-xl border border-primary/20 p-6 shadow-lg">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
          <h3 class="text-xl font-bold text-white mb-4 md:mb-0">系统运行数据 <span class="text-neon-blue text-sm font-normal">实时更新</span></h3>
          <div class="flex space-x-3">
            <button class="px-3 py-1 bg-primary/20 text-white text-sm rounded hover:bg-primary/30 transition-colors">今日</button>
            <button class="px-3 py-1 bg-primary/10 text-gray-400 text-sm rounded hover:bg-primary/30 transition-colors">本周</button>
            <button class="px-3 py-1 bg-primary/10 text-gray-400 text-sm rounded hover:bg-primary/30 transition-colors">本月</button>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- 图表1 -->
          <div class="bg-dark/30 rounded-lg p-4 border border-primary/20">
            <h4 class="text-white text-sm font-medium mb-3">食堂运营效率</h4>
            <div class="h-48">
              <canvas id="efficiencyChart"></canvas>
            </div>
          </div>

          <!-- 图表2 -->
          <div class="bg-dark/30 rounded-lg p-4 border border-primary/20">
            <h4 class="text-white text-sm font-medium mb-3">食品安全检测</h4>
            <div class="h-48">
              <canvas id="safetyChart"></canvas>
            </div>
          </div>

          <!-- 图表3 -->
          <div class="bg-dark/30 rounded-lg p-4 border border-primary/20">
            <h4 class="text-white text-sm font-medium mb-3">用户满意度</h4>
            <div class="h-48">
              <canvas id="satisfactionChart"></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- 核心功能 -->
  <section id="features" class="section-padding bg-dark-blue relative overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute top-1/4 -left-20 w-60 h-60 bg-neon-purple/10 rounded-full blur-3xl"></div>
    <div class="absolute bottom-1/4 -right-20 w-60 h-60 bg-neon-blue/10 rounded-full blur-3xl"></div>

    <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
      <div class="text-center max-w-3xl mx-auto mb-16">
        <h2 class="text-[clamp(1.8rem,4vw,2.8rem)] font-bold text-white mb-4">八大智能化功能，全面保障食堂安全管理</h2>
        <p class="text-lg text-gray-300">
          我们的智慧食堂平台集成了多项先进功能，从食材采购到餐后服务，全方位提升食堂管理效率与食品安全
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        <!-- 功能卡片1 -->
        <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 group">
          <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center mb-5 group-hover:bg-primary/30 transition-colors">
            <i class="fas fa-search text-neon-blue text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">智能检查系统</h3>
          <p class="text-gray-400">
            员工通过扫码上传食堂卫生状况、设备运行情况，管理员在线进行评价反馈，实时监控食堂运营状态
          </p>
        </div>

        <!-- 功能卡片2 -->
        <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 group">
          <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center mb-5 group-hover:bg-primary/30 transition-colors">
            <i class="fas fa-users text-neon-blue text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">家校共陪餐</h3>
          <p class="text-gray-400">
            邀请家长参与陪餐体验，提升食堂管理透明度，加强家校互动沟通，增强家长对食堂的信任度
          </p>
        </div>

        <!-- 功能卡片3 -->
        <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 group">
          <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center mb-5 group-hover:bg-primary/30 transition-colors">
            <i class="far fa-file-alt text-neon-blue text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">智能日志生成</h3>
          <p class="text-gray-400">
            每日自动生成食堂工作日志，完整记录运营情况，基于数据进行分析，为管理决策提供依据
          </p>
        </div>

        <!-- 功能卡片4 -->
        <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 group">
          <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center mb-5 group-hover:bg-primary/30 transition-colors">
            <i class="fas fa-list text-neon-blue text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">灵活菜单管理</h3>
          <p class="text-gray-400">
            支持周菜单灵活安排与调整，可直接打印输出，一键导入菜单信息生成采购计划，提高工作效率
          </p>
        </div>

        <!-- 功能卡片5 -->
        <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 group">
          <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center mb-5 group-hover:bg-primary/30 transition-colors">
            <i class="fas fa-shopping-cart text-neon-blue text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">智能采购系统</h3>
          <p class="text-gray-400">
            提供供应商灵活选择功能，支持智能价格对比，依据采购需求自动生成采购单，简化采购流程
          </p>
        </div>

        <!-- 功能卡片6 -->
        <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 group">
          <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center mb-5 group-hover:bg-primary/30 transition-colors">
            <i class="fas fa-archive text-neon-blue text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">出入库管理</h3>
          <p class="text-gray-400">
            对出入库流程进行完整管理，自动生成台账报表，实时监控库存情况，确保库存管理精准高效
          </p>
        </div>

        <!-- 功能卡片7 -->
        <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 group">
          <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center mb-5 group-hover:bg-primary/30 transition-colors">
            <i class="fas fa-search-minus text-neon-blue text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">全程溯源</h3>
          <p class="text-gray-400">
            实现食品从源头到餐桌的全程可追溯，打造透明化供应链，明确安全责任到人
          </p>
        </div>

        <!-- 功能卡片8 -->
        <div class="bg-dark/30 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-primary/20 group">
          <div class="w-14 h-14 bg-primary/20 rounded-lg flex items-center justify-center mb-5 group-hover:bg-primary/30 transition-colors">
            <i class="fas fa-barcode text-neon-blue text-2xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">一键式留样标签打印</h3>
          <p class="text-gray-400">
            规范食品留样流程，自动生成留样标签，确保食品安全管理符合规范要求
          </p>
        </div>
      </div>
    </div>
  </section>

  <!-- 系统优势 -->
  <section id="advantages" class="section-padding bg-gradient-to-b from-dark-blue to-dark relative overflow-hidden clip-path-slant">
    <!-- 背景装饰 -->
    <div class="absolute top-1/3 -left-40 w-80 h-80 bg-neon-blue/10 rounded-full blur-3xl"></div>
    <div class="absolute bottom-1/3 -right-40 w-80 h-80 bg-neon-purple/10 rounded-full blur-3xl"></div>

    <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
      <div class="text-center max-w-3xl mx-auto mb-16">
        <h2 class="text-[clamp(1.8rem,4vw,2.8rem)] font-bold text-white mb-4">系统优势</h2>
        <p class="text-lg text-gray-300">
          我们的智慧食堂管理平台不仅功能强大，还具备多项核心优势，为校园食堂管理带来全方位提升
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        <!-- 优势1 -->
        <div class="bg-dark/50 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-neon-blue/30 group">
          <div class="w-12 h-12 bg-neon-blue/20 rounded-full flex items-center justify-center text-neon-blue mb-4 group-hover:bg-neon-blue/30 transition-colors">
            <i class="fas fa-chart-line text-xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">数据分析决策</h3>
          <p class="text-gray-400">
            通过智能数据分析，助力管理者做出科学合理的决策，优化食堂运营效率
          </p>
        </div>

        <!-- 优势2 -->
        <div class="bg-dark/50 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-neon-blue/30 group">
          <div class="w-12 h-12 bg-neon-blue/20 rounded-full flex items-center justify-center text-neon-blue mb-4 group-hover:bg-neon-blue/30 transition-colors">
            <i class="fas fa-shield-alt text-xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">食品安全追溯</h3>
          <p class="text-gray-400">
            建立完善的追溯体系，全方位保障食品安全，让家长和师生更放心
          </p>
        </div>

        <!-- 优势3 -->
        <div class="bg-dark/50 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-neon-blue/30 group">
          <div class="w-12 h-12 bg-neon-blue/20 rounded-full flex items-center justify-center text-neon-blue mb-4 group-hover:bg-neon-blue/30 transition-colors">
            <i class="fas fa-eye text-xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">实时监控</h3>
          <p class="text-gray-400">
            对食堂运营各环节进行实时监控，及时发现问题并解决，防患于未然
          </p>
        </div>

        <!-- 优势4 -->
        <div class="bg-dark/50 backdrop-blur-sm rounded-xl shadow-md p-6 card-hover border border-neon-blue/30 group">
          <div class="w-12 h-12 bg-neon-blue/20 rounded-full flex items-center justify-center text-neon-blue mb-4 group-hover:bg-neon-blue/30 transition-colors">
            <i class="fas fa-cogs text-xl"></i>
          </div>
          <h3 class="text-xl font-bold mb-3 text-white">流程优化</h3>
          <p class="text-gray-400">
            简化食堂管理流程，减少人工操作，提高工作效率，降低管理成本
          </p>
        </div>
      </div>
    </div>
  </section>

  <!-- 管理流程 -->
  <section id="process" class="section-padding bg-dark relative overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute top-1/4 -left-32 w-64 h-64 bg-neon-purple/10 rounded-full blur-3xl"></div>
    <div class="absolute bottom-1/4 -right-32 w-64 h-64 bg-neon-blue/10 rounded-full blur-3xl"></div>

    <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
      <div class="text-center max-w-3xl mx-auto mb-16">
        <h2 class="text-[clamp(1.8rem,4vw,2.8rem)] font-bold text-white mb-4">智能化管理流程</h2>
        <p class="text-lg text-gray-300">
          我们的智慧食堂管理平台通过科学的流程设计，实现了从食材采购到餐桌服务的全流程智能化管理
        </p>
      </div>

      <div class="relative">
        <!-- 流程线 -->
        <div class="hidden md:block absolute left-1/2 top-0 bottom-0 w-1 bg-gradient-to-b from-neon-blue to-neon-purple transform -translate-x-1/2"></div>

        <!-- 流程步骤 -->
        <div class="space-y-12 relative">
          <!-- 步骤1 -->
          <div class="flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 md:pr-12 md:text-right mb-8 md:mb-0">
              <h3 class="text-xl font-bold text-white mb-3">智能采购</h3>
              <p class="text-gray-400">基于历史数据和当前库存，系统自动生成采购计划，智能匹配优质供应商，实现高效采购流程。</p>
            </div>
            <div class="md:w-12 flex justify-center">
              <div class="w-12 h-12 bg-neon-blue rounded-full flex items-center justify-center shadow-lg z-10 text-white font-bold">1</div>
            </div>
            <div class="md:w-1/2 md:pl-12 mt-8 md:mt-0">
              <img src="{{ url_for('static', filename='images/process-1.jpg') }}" alt="智能采购" class="w-full h-auto rounded-lg shadow-lg">
            </div>
          </div>

          <!-- 步骤2 -->
          <div class="flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 md:pr-12 order-1 md:order-2 mt-8 md:mt-0">
              <img src="{{ url_for('static', filename='images/process-2.jpg') }}" alt="食材验收" class="w-full h-auto rounded-lg shadow-lg">
            </div>
            <div class="md:w-12 flex justify-center order-2">
              <div class="w-12 h-12 bg-neon-purple rounded-full flex items-center justify-center shadow-lg z-10 text-white font-bold">2</div>
            </div>
            <div class="md:w-1/2 md:pl-12 order-3 mb-8 md:mb-0">
              <h3 class="text-xl font-bold text-white mb-3">食材验收</h3>
              <p class="text-gray-400">通过扫码快速完成食材验收，记录食材来源、检验结果等信息，确保食材安全可追溯。</p>
            </div>
          </div>

          <!-- 步骤3 -->
          <div class="flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 md:pr-12 md:text-right mb-8 md:mb-0">
              <h3 class="text-xl font-bold text-white mb-3">库存管理</h3>
              <p class="text-gray-400">实时监控库存状态，自动预警库存不足或过期食品，支持先进先出的库存管理原则。</p>
            </div>
            <div class="md:w-12 flex justify-center">
              <div class="w-12 h-12 bg-neon-green rounded-full flex items-center justify-center shadow-lg z-10 text-white font-bold">3</div>
            </div>
            <div class="md:w-1/2 md:pl-12 mt-8 md:mt-0">
              <img src="{{ url_for('static', filename='images/process-3.jpg') }}" alt="库存管理" class="w-full h-auto rounded-lg shadow-lg">
            </div>
          </div>

          <!-- 步骤4 -->
          <div class="flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 md:pr-12 order-1 md:order-2 mt-8 md:mt-0">
              <img src="{{ url_for('static', filename='images/process-4.jpg') }}" alt="餐饮制作" class="w-full h-auto rounded-lg shadow-lg">
            </div>
            <div class="md:w-12 flex justify-center order-2">
              <div class="w-12 h-12 bg-neon-blue rounded-full flex items-center justify-center shadow-lg z-10 text-white font-bold">4</div>
            </div>
            <div class="md:w-1/2 md:pl-12 order-3 mb-8 md:mb-0">
              <h3 class="text-xl font-bold text-white mb-3">餐饮制作</h3>
              <p class="text-gray-400">标准化食谱管理，智能安排厨师工作量，实时监控厨房操作规范，确保餐饮质量。</p>
            </div>
          </div>

          <!-- 步骤5 -->
          <div class="flex flex-col md:flex-row items-center">
            <div class="md:w-1/2 md:pr-12 md:text-right mb-8 md:mb-0">
              <h3 class="text-xl font-bold text-white mb-3">智能供餐</h3>
              <p class="text-gray-400">自助点餐、智能结算系统，减少排队时间，提高供餐效率，同时收集用户反馈数据。</p>
            </div>
            <div class="md:w-12 flex justify-center">
              <div class="w-12 h-12 bg-neon-purple rounded-full flex items-center justify-center shadow-lg z-10 text-white font-bold">5</div>
            </div>
            <div class="md:w-1/2 md:pl-12 mt-8 md:mt-0">
              <img src="{{ url_for('static', filename='images/process-5.jpg') }}" alt="智能供餐" class="w-full h-auto rounded-lg shadow-lg">
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- 联系我们 -->
  <section id="contact" class="section-padding bg-gradient-to-b from-dark-blue to-dark relative overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute top-1/3 -left-32 w-64 h-64 bg-neon-green/10 rounded-full blur-3xl"></div>
    <div class="absolute bottom-1/3 -right-32 w-64 h-64 bg-neon-purple/10 rounded-full blur-3xl"></div>

    <div class="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
      <div class="text-center max-w-3xl mx-auto mb-16">
        <h2 class="text-[clamp(1.8rem,4vw,2.8rem)] font-bold text-white mb-4">开始您的智慧食堂管理之旅</h2>
        <p class="text-lg text-gray-300">
          让食品安全管理更简单、更智能、更透明。立即体验我们的智慧食堂管理平台
        </p>
      </div>

      <div class="flex flex-col lg:flex-row justify-center items-center gap-8">
        <a href="{{ url_for('auth.login') }}" class="bg-gradient-to-r from-primary to-neon-blue text-white font-medium px-12 py-4 rounded-lg transition-all shadow-lg hover:shadow-neon-blue/30 flex items-center text-lg neon-glow">
          <i class="fas fa-rocket mr-3"></i>
          立即体验系统
        </a>
        <a href="{{ url_for('auth.register') }}" class="bg-transparent hover:bg-white/10 text-white border border-neon-blue/50 font-medium px-12 py-4 rounded-lg transition-all flex items-center text-lg">
          <i class="fas fa-user-plus mr-3"></i>
          注册新账号
        </a>
      </div>

      <!-- 联系信息 -->
      <div class="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
        <div class="text-center">
          <div class="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-phone text-neon-blue text-2xl"></i>
          </div>
          <h3 class="text-white font-semibold mb-2">技术支持</h3>
          <p class="text-gray-400">************</p>
        </div>
        <div class="text-center">
          <div class="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-envelope text-neon-blue text-2xl"></i>
          </div>
          <h3 class="text-white font-semibold mb-2">邮箱联系</h3>
          <p class="text-gray-400"><EMAIL></p>
        </div>
        <div class="text-center">
          <div class="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-clock text-neon-blue text-2xl"></i>
          </div>
          <h3 class="text-white font-semibold mb-2">服务时间</h3>
          <p class="text-gray-400">7×24小时在线服务</p>
        </div>
      </div>
    </div>
  </section>

  <!-- 页脚 -->
  <footer class="bg-dark py-8 border-t border-primary/20">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center">
        <div class="flex items-center justify-center space-x-2 mb-4">
          <div class="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
            <i class="fas fa-utensils text-white"></i>
          </div>
          <span class="text-xl font-bold text-white">智慧食堂<span class="text-neon-blue">平台</span></span>
        </div>
        <p class="text-gray-400 mb-4">
          © 2024 智慧食堂管理平台. 保留所有权利.
        </p>
        <div class="flex justify-center space-x-6">
          <a href="#" class="text-gray-400 hover:text-neon-blue transition-colors">隐私政策</a>
          <a href="#" class="text-gray-400 hover:text-neon-blue transition-colors">服务条款</a>
          <a href="#" class="text-gray-400 hover:text-neon-blue transition-colors">帮助中心</a>
        </div>
      </div>
    </div>
  </footer>

  <!-- JavaScript -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // 移动端菜单切换
      const menuToggle = document.getElementById('menu-toggle');
      const mobileMenu = document.getElementById('mobile-menu');

      menuToggle.addEventListener('click', function() {
        mobileMenu.classList.toggle('hidden');
      });

      // 导航栏滚动效果
      window.addEventListener('scroll', function() {
        const navbar = document.getElementById('navbar');
        if (window.scrollY > 50) {
          navbar.classList.add('bg-dark-blue/95');
          navbar.classList.remove('bg-dark-blue/80');
        } else {
          navbar.classList.add('bg-dark-blue/80');
          navbar.classList.remove('bg-dark-blue/95');
        }
      });

      // 平滑滚动
      document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
          e.preventDefault();
          const target = document.querySelector(this.getAttribute('href'));
          if (target) {
            target.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
          }
        });
      });

      // 初始化图表
      initCharts();
    });

    // 图表初始化
    function initCharts() {
      // 运营效率图表
      const efficiencyCtx = document.getElementById('efficiencyChart');
      if (efficiencyCtx) {
        new Chart(efficiencyCtx, {
          type: 'line',
          data: {
            labels: ['周一', '周二', '周三', '周四', '周五'],
            datasets: [{
              label: '效率指数',
              data: [85, 92, 88, 95, 90],
              borderColor: '#00BFFF',
              backgroundColor: 'rgba(0, 191, 255, 0.1)',
              borderWidth: 2,
              fill: true,
              tension: 0.4
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                display: false
              }
            },
            scales: {
              x: {
                grid: {
                  color: 'rgba(255, 255, 255, 0.1)'
                },
                ticks: {
                  color: '#9CA3AF',
                  font: {
                    size: 10
                  }
                }
              },
              y: {
                grid: {
                  color: 'rgba(255, 255, 255, 0.1)'
                },
                ticks: {
                  color: '#9CA3AF',
                  font: {
                    size: 10
                  }
                }
              }
            }
          }
        });
      }

      // 安全检测图表
      const safetyCtx = document.getElementById('safetyChart');
      if (safetyCtx) {
        new Chart(safetyCtx, {
          type: 'doughnut',
          data: {
            labels: ['合格', '优秀', '待改进'],
            datasets: [{
              data: [70, 25, 5],
              backgroundColor: ['#00FF9D', '#00BFFF', '#9D4EDD'],
              borderWidth: 0
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                display: false
              }
            }
          }
        });
      }

      // 满意度图表
      const satisfactionCtx = document.getElementById('satisfactionChart');
      if (satisfactionCtx) {
        new Chart(satisfactionCtx, {
          type: 'bar',
          data: {
            labels: ['本周', '上周', '上月'],
            datasets: [{
              label: '满意度',
              data: [4.8, 4.6, 4.5],
              backgroundColor: ['#9D4EDD', '#00BFFF', '#00FF9D'],
              borderWidth: 0
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                display: false
              }
            },
            scales: {
              x: {
                grid: {
                  display: false
                },
                ticks: {
                  color: '#9CA3AF',
                  font: {
                    size: 10
                  }
                }
              },
              y: {
                grid: {
                  color: 'rgba(255, 255, 255, 0.1)'
                },
                ticks: {
                  color: '#9CA3AF',
                  font: {
                    size: 10
                  }
                }
              }
            }
          }
        });
      }
    }
  </script>
</body>
</html>
