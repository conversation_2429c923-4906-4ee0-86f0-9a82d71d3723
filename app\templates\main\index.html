{% extends 'base.html' %}

{% block title %}首页 - {{ super() }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/home.css') }}">
<style>
/* 现代化首页样式增强 */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.smart-card {
    transition: all 0.3s ease;
    border: none;
    border-radius: 16px;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.smart-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

.feature-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    padding: 2rem;
    height: 100%;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

.stats-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 4rem 0;
    position: relative;
}

.stat-item {
    text-align: center;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 16px;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.stat-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.btn-light {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 12px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.btn-light:hover {
    background: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.btn-outline-light {
    border: 2px solid rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(255, 255, 255, 0.3);
}

.animate-fadeInUp {
    animation: fadeInUp 0.8s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.aos-animate {
    animation: fadeInUp 0.8s ease forwards;
}
</style>
{% endblock %}

{% block content %}
{% from 'components/home_components.html' import feature_card, stat_card, feature_item, cta_section %}

<div class="hero-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-10 mx-auto text-center">
                <h1 class="display-4 mb-4 animate-fadeInUp aos-animate">校园餐智慧食堂平台<br><small class="text-light">(Smart Canteen Management Platform)</small></h1>
                <p class="lead mb-5 animate-fadeInUp aos-animate">全方位智能化食堂管理解决方案，让食品安全看得见、管得住、可追溯</p>

                <div class="d-flex justify-content-center gap-3 mb-4">
                    <a href="{{ url_for('auth.login') }}" class="btn btn-light btn-lg px-5">
                        <i class="fas fa-sign-in-alt me-2"></i>立即体验
                    </a>
                    <a href="#features" class="btn btn-outline-light btn-lg px-5">
                        <i class="fas fa-info-circle me-2"></i>了解更多
                    </a>
                </div>
            </div>
        </div>
    </div>
    <!-- SVG波浪背景 -->
    <div class="wave-bg">
        <svg viewBox="0 0 1440 120" width="100%" height="120" xmlns="http://www.w3.org/2000/svg"><path fill="#fff" fill-opacity="1" d="M0,64L48,69.3C96,75,192,85,288,101.3C384,117,480,139,576,133.3C672,128,768,96,864,85.3C960,75,1056,85,1152,101.3C1248,117,1344,139,1392,149.3L1440,160L1440,0L1392,0C1344,0,1248,0,1152,0C1056,0,960,0,864,0C768,0,672,0,576,0C480,0,384,0,288,0C192,0,96,0,48,0L0,0Z"></path></svg>
    </div>
</div>

<!-- 核心亮点功能展示 -->
<div id="features" class="container">
    <div class="row text-center mb-5">
        <div class="col-12">
            <h2 class="display-5 mb-3 animate-fadeInUp aos-animate">🌟 核心功能亮点</h2>
            <p class="lead text-muted animate-fadeInUp aos-animate">八大智能化功能，全面保障食堂安全管理</p>
        </div>
    </div>

    <div class="row mb-5">
        <div class="col-md-4 mb-4 aos-animate">
            <div class="feature-card smart-card">
                <div class="text-center mb-3">
                    <i class="fas fa-qrcode text-primary" style="font-size: 3rem;"></i>
                </div>
                <h4 class="text-center mb-3">智能检查系统</h4>
                <p class="text-muted text-center mb-3">员工扫码上传食堂卫生、设备情况，管理员在线评价，实时监控运营状态</p>
                <div class="text-center">
                    <span class="badge bg-primary me-2">扫码上传</span>
                    <span class="badge bg-success">在线评价</span>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4 aos-animate">
            <div class="feature-card smart-card">
                <div class="text-center mb-3">
                    <i class="fas fa-users text-success" style="font-size: 3rem;"></i>
                </div>
                <h4 class="text-center mb-3">家校共陪餐</h4>
                <p class="text-muted text-center mb-3">家长参与陪餐体验，透明化食堂管理，增强家校信任</p>
                <div class="text-center">
                    <span class="badge bg-success me-2">透明管理</span>
                    <span class="badge bg-info">家校互动</span>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4 aos-animate">
            <div class="feature-card smart-card">
                <div class="text-center mb-3">
                    <i class="fas fa-file-alt text-info" style="font-size: 3rem;"></i>
                </div>
                <h4 class="text-center mb-3">智能日志生成</h4>
                <p class="text-muted text-center mb-3">每天自动生成工作日志，全面记录食堂运营情况，数据化管理决策</p>
                <div class="text-center">
                    <span class="badge bg-info me-2">自动生成</span>
                    <span class="badge bg-warning">数据分析</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 其他核心功能 -->
    <div class="row mb-5">
        <div class="col-md-3 mb-3 aos-animate">
            {{ feature_item(
                icon='fas fa-calendar-alt text-warning',
                title='灵活菜单管理',
                description='周菜单灵活安排，支持打印，一键导入采购计划'
            ) }}
        </div>
        <div class="col-md-3 mb-3 aos-animate">
            {{ feature_item(
                icon='fas fa-shopping-cart text-danger',
                title='智能采购系统',
                description='灵活选择供应商，智能价格对比，自动生成采购单'
            ) }}
        </div>
        <div class="col-md-3 mb-3 aos-animate">
            {{ feature_item(
                icon='fas fa-warehouse text-purple',
                title='出入库管理',
                description='完整流程管理，自动生成台账报表，库存实时监控'
            ) }}
        </div>
        <div class="col-md-3 mb-3 aos-animate">
            {{ feature_item(
                icon='fas fa-search text-dark',
                title='全程溯源',
                description='食品全程可追溯，供应链透明化，安全责任到人'
            ) }}
        </div>
    </div>

    <!-- 特色功能亮点 -->
    <div class="row mb-5">
        <div class="col-md-6 mb-3 aos-animate">
            {{ feature_item(
                icon='fas fa-print text-primary',
                title='一键式留样标签打印',
                description='规范化留样流程，自动生成标签，确保食品安全'
            ) }}
        </div>
        <div class="col-md-6 mb-3 aos-animate">
            {{ feature_item(
                icon='fas fa-chart-line text-success',
                title='数据分析决策',
                description='智能数据分析，帮助管理者做出科学决策'
            ) }}
        </div>
    </div>
</div>

<!-- 统计数据展示 -->
<div class="stats-section">
    <div class="container">
        <div class="row">
            <div class="col-md-3 aos-animate">
                <div class="stat-item">
                    <div class="stat-number" id="stat-safe">0</div>
                    <div class="stat-label">食品安全追溯</div>
                </div>
            </div>
            <div class="col-md-3 aos-animate">
                <div class="stat-item">
                    <div class="stat-number" id="stat-monitor">0</div>
                    <div class="stat-label">实时监控</div>
                </div>
            </div>
            <div class="col-md-3 aos-animate">
                <div class="stat-item">
                    <div class="stat-number" id="stat-intel">0</div>
                    <div class="stat-label">管理流程</div>
                </div>
            </div>
            <div class="col-md-3 aos-animate">
                <div class="stat-item">
                    <div class="stat-number" id="stat-trans">0</div>
                    <div class="stat-label">家校共管</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 行动号召 -->
{{ cta_section(
    title='开始您的智慧食堂管理之旅',
    description='让食品安全管理更简单、更智能、更透明',
    primary_btn='<a href="' + url_for('auth.login') + '" class="btn btn-primary btn-lg px-5"><i class="fas fa-rocket me-2"></i>立即开始</a>',
    secondary_btn='<a href="' + url_for('auth.register') + '" class="btn btn-outline-primary btn-lg px-5"><i class="fas fa-user-plus me-2"></i>注册账号</a>'
) }}
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
// 滚动淡入动画
function handleAOS() {
    const elements = document.querySelectorAll('.aos-animate');
    const windowHeight = window.innerHeight;
    elements.forEach(el => {
        const rect = el.getBoundingClientRect();
        if (rect.top < windowHeight - 40) {
            el.classList.add('aos-animate');
        }
    });
}
document.addEventListener('scroll', handleAOS);
document.addEventListener('DOMContentLoaded', handleAOS);

// 统计数字滚动动画
function animateCount(id, target, duration=1200) {
    const el = document.getElementById(id);
    if (!el) return;
    let start = 0;
    const step = Math.ceil(target / (duration / 16));
    function update() {
        start += step;
        if (start >= target) {
            el.textContent = target;
        } else {
            el.textContent = start;
            requestAnimationFrame(update);
        }
    }
    update();
}
document.addEventListener('DOMContentLoaded', function() {
    animateCount('stat-safe', 100);
    animateCount('stat-monitor', 24);
    animateCount('stat-intel', 100);
    animateCount('stat-trans', 100);
});

// 平滑滚动
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth'
            });
        }
    });
});
</script>
{% endblock %}
