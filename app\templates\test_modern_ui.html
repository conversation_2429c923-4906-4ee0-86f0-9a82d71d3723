{% extends 'base.html' %}

{% block title %}现代化界面测试 - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 现代化导航栏测试 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="smart-card">
                <div class="card-header">
                    <h4><i class="fas fa-palette me-2"></i>现代化界面测试</h4>
                </div>
                <div class="card-body">
                    <p class="lead">欢迎体验智慧食堂管理系统的现代化界面！</p>
                    <p>这个页面展示了我们新的设计系统，包括现代化的卡片、按钮、动画效果等。</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 功能卡片展示 -->
    <div class="row mb-4">
        <div class="col-md-4 mb-3">
            <div class="smart-card">
                <div class="card-body text-center">
                    <i class="fas fa-qrcode text-primary mb-3" style="font-size: 3rem;"></i>
                    <h5>智能检查</h5>
                    <p class="text-muted">扫码上传，在线评价</p>
                    <button class="smart-btn">
                        <i class="fas fa-arrow-right me-2"></i>立即体验
                    </button>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="smart-card">
                <div class="card-body text-center">
                    <i class="fas fa-users text-success mb-3" style="font-size: 3rem;"></i>
                    <h5>家校共管</h5>
                    <p class="text-muted">透明管理，家校互动</p>
                    <button class="smart-btn">
                        <i class="fas fa-arrow-right me-2"></i>了解更多
                    </button>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="smart-card">
                <div class="card-body text-center">
                    <i class="fas fa-chart-line text-info mb-3" style="font-size: 3rem;"></i>
                    <h5>数据分析</h5>
                    <p class="text-muted">智能分析，科学决策</p>
                    <button class="smart-btn">
                        <i class="fas fa-arrow-right me-2"></i>查看报表
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 表格样式测试 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="smart-table">
                <table class="table table-hover mb-0">
                    <thead>
                        <tr>
                            <th>功能模块</th>
                            <th>状态</th>
                            <th>更新时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><i class="fas fa-utensils me-2"></i>菜单管理</td>
                            <td><span class="badge bg-success">正常</span></td>
                            <td>2024-01-15 10:30</td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary me-1">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-info">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-shopping-cart me-2"></i>采购管理</td>
                            <td><span class="badge bg-warning">待处理</span></td>
                            <td>2024-01-15 09:15</td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary me-1">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-info">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </td>
                        </tr>
                        <tr>
                            <td><i class="fas fa-warehouse me-2"></i>库存管理</td>
                            <td><span class="badge bg-success">正常</span></td>
                            <td>2024-01-15 08:45</td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary me-1">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-info">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 按钮样式测试 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="smart-card">
                <div class="card-header">
                    <h5><i class="fas fa-mouse-pointer me-2"></i>按钮样式展示</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex flex-wrap gap-2">
                        <button class="smart-btn">
                            <i class="fas fa-plus me-2"></i>新增
                        </button>
                        <button class="btn btn-outline-primary">
                            <i class="fas fa-edit me-2"></i>编辑
                        </button>
                        <button class="btn btn-outline-success">
                            <i class="fas fa-check me-2"></i>确认
                        </button>
                        <button class="btn btn-outline-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>警告
                        </button>
                        <button class="btn btn-outline-danger">
                            <i class="fas fa-trash me-2"></i>删除
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主题切换测试 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="smart-card">
                <div class="card-header">
                    <h5><i class="fas fa-palette me-2"></i>主题切换</h5>
                </div>
                <div class="card-body">
                    <p>点击下面的颜色来切换主题：</p>
                    <div class="d-flex flex-wrap gap-2">
                        <button class="btn btn-outline-secondary" onclick="switchTheme('default')">
                            <span class="theme-preview" style="background: linear-gradient(135deg, #667eea, #764ba2);"></span>
                            默认主题
                        </button>
                        <button class="btn btn-outline-primary" onclick="switchTheme('primary')">
                            <span class="theme-preview primary"></span>
                            蓝色主题
                        </button>
                        <button class="btn btn-outline-success" onclick="switchTheme('success')">
                            <span class="theme-preview success"></span>
                            绿色主题
                        </button>
                        <button class="btn btn-outline-warning" onclick="switchTheme('warning')">
                            <span class="theme-preview warning"></span>
                            橙色主题
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 通知测试 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="smart-card">
                <div class="card-header">
                    <h5><i class="fas fa-bell me-2"></i>通知系统测试</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex flex-wrap gap-2">
                        <button class="btn btn-outline-info" onclick="showNotification('这是一条信息通知', 'info')">
                            信息通知
                        </button>
                        <button class="btn btn-outline-success" onclick="showNotification('操作成功！', 'success')">
                            成功通知
                        </button>
                        <button class="btn btn-outline-warning" onclick="showNotification('请注意！', 'warning')">
                            警告通知
                        </button>
                        <button class="btn btn-outline-danger" onclick="showNotification('发生错误！', 'error')">
                            错误通知
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 主题切换函数
function switchTheme(theme) {
    if (typeof window.switchTheme === 'function') {
        window.switchTheme(theme);
    } else {
        console.log('切换主题:', theme);
        showNotification('主题已切换为: ' + theme, 'success');
    }
}

// 通知函数
function showNotification(message, type) {
    if (typeof window.showNotification === 'function') {
        window.showNotification(message, type);
    } else {
        alert(message);
    }
}
</script>
{% endblock %}
