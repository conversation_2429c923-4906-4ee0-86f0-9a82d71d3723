#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户区域、角色、权限属性分析工具
分析系统中用户的区域分布、角色分配、权限配置等情况
"""

import os
import sys
import json
from datetime import datetime
from collections import defaultdict, Counter

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app import create_app, db
    from app.models import User, Role, UserRole, AdministrativeArea
    from app.utils.permissions import parse_permissions_json
    from sqlalchemy import text
    print("✅ 模块导入成功")
except Exception as e:
    print(f"❌ 模块导入失败: {e}")
    sys.exit(1)

def analyze_user_attributes():
    """分析用户的区域、角色、权限属性"""

    try:
        app = create_app()
        print("✅ Flask应用创建成功")
    except Exception as e:
        print(f"❌ Flask应用创建失败: {e}")
        return

    with app.app_context():
        try:
            # 测试数据库连接
            db.session.execute(text("SELECT 1")).fetchone()
            print("✅ 数据库连接成功")
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return

        print("=" * 80)
        print("用户区域、角色、权限属性分析报告")
        print("=" * 80)
        print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()

        # 1. 基础统计信息
        print("1. 基础统计信息")
        print("-" * 40)

        total_users = User.query.count()
        active_users = User.query.filter_by(status=1).count()
        total_roles = Role.query.count()
        total_areas = AdministrativeArea.query.count()

        print(f"总用户数: {total_users}")
        print(f"活跃用户数: {active_users}")
        print(f"总角色数: {total_roles}")
        print(f"总区域数: {total_areas}")
        print()

        # 2. 区域分析
        print("2. 用户区域分布分析")
        print("-" * 40)

        # 按区域级别统计用户分布
        area_level_stats = defaultdict(int)
        area_distribution = defaultdict(list)

        users_with_area = User.query.filter(User.area_id.isnot(None)).all()
        users_without_area = User.query.filter(User.area_id.is_(None)).all()

        print(f"有区域关联的用户: {len(users_with_area)}")
        print(f"无区域关联的用户: {len(users_without_area)}")
        print()

        for user in users_with_area:
            if user.area:
                level_name = get_area_level_name(user.area.level)
                area_level_stats[level_name] += 1
                area_distribution[user.area.name].append({
                    'id': user.id,
                    'username': user.username,
                    'real_name': user.real_name,
                    'roles': [role.name for role in user.roles]
                })

        print("按区域级别统计:")
        for level, count in area_level_stats.items():
            print(f"  {level}: {count} 用户")
        print()

        # 显示前10个用户最多的区域
        print("用户数量最多的前10个区域:")
        sorted_areas = sorted(area_distribution.items(), key=lambda x: len(x[1]), reverse=True)[:10]
        for area_name, users in sorted_areas:
            print(f"  {area_name}: {len(users)} 用户")
        print()

        # 3. 角色分析
        print("3. 角色分配分析")
        print("-" * 40)

        roles = Role.query.all()
        role_stats = {}

        for role in roles:
            user_count = UserRole.query.filter_by(role_id=role.id).count()
            role_stats[role.name] = {
                'user_count': user_count,
                'description': role.description,
                'permissions': parse_permissions_json(role.permissions)
            }

        print("角色使用统计:")
        for role_name, stats in sorted(role_stats.items(), key=lambda x: x[1]['user_count'], reverse=True):
            print(f"  {role_name}: {stats['user_count']} 用户")
            if stats['description']:
                print(f"    描述: {stats['description']}")
        print()

        # 4. 权限分析
        print("4. 权限配置分析")
        print("-" * 40)

        # 统计各模块的权限分配情况
        module_permissions = defaultdict(set)
        permission_usage = defaultdict(int)

        for role_name, stats in role_stats.items():
            permissions = stats['permissions']
            if '*' in permissions and '*' in permissions['*']:
                print(f"  {role_name}: 拥有全部权限 (超级管理员)")
                continue

            for module, actions in permissions.items():
                if module == '*':
                    continue
                module_permissions[module].update(actions)
                for action in actions:
                    permission_usage[f"{module}:{action}"] += stats['user_count']

        print("\n各模块权限配置:")
        for module, actions in sorted(module_permissions.items()):
            print(f"  {module}:")
            for action in sorted(actions):
                if action == '*':
                    print(f"    * (全部权限)")
                else:
                    print(f"    {action}")
        print()

        # 5. 用户详细分析
        print("5. 用户详细分析")
        print("-" * 40)

        # 分析多角色用户
        multi_role_users = []
        single_role_users = []
        no_role_users = []

        for user in User.query.all():
            role_count = len(user.roles)
            if role_count == 0:
                no_role_users.append(user)
            elif role_count == 1:
                single_role_users.append(user)
            else:
                multi_role_users.append(user)

        print(f"无角色用户: {len(no_role_users)}")
        print(f"单角色用户: {len(single_role_users)}")
        print(f"多角色用户: {len(multi_role_users)}")
        print()

        if multi_role_users:
            print("多角色用户详情:")
            for user in multi_role_users[:10]:  # 只显示前10个
                roles_str = ", ".join([role.name for role in user.roles])
                area_name = user.area.name if user.area else "无区域"
                print(f"  {user.username} ({user.real_name}): {roles_str} - {area_name}")
            if len(multi_role_users) > 10:
                print(f"  ... 还有 {len(multi_role_users) - 10} 个多角色用户")
        print()

        # 继续分析...
        analyze_area_permissions(users_with_area)
        identify_issues(users_without_area, no_role_users, roles)
        provide_suggestions(users_without_area, no_role_users, multi_role_users, single_role_users)

        print("\n" + "=" * 80)
        print("分析完成")

def get_area_level_name(level):
    """获取区域级别名称"""
    level_names = {
        1: "县市区",
        2: "乡镇",
        3: "学校",
        4: "食堂"
    }
    return level_names.get(level, f"未知级别({level})")

def analyze_area_permissions(users_with_area):
    """分析区域权限"""
    print("6. 区域权限分析")
    print("-" * 40)

    # 分析各区域级别的权限分布
    area_permission_analysis = defaultdict(lambda: defaultdict(set))

    for user in users_with_area:
        if user.area:
            level_name = get_area_level_name(user.area.level)
            for role in user.roles:
                permissions = parse_permissions_json(role.permissions)
                for module, actions in permissions.items():
                    area_permission_analysis[level_name][module].update(actions)

    for level_name, modules in area_permission_analysis.items():
        print(f"{level_name}级别用户权限:")
        for module, actions in sorted(modules.items()):
            if '*' in actions:
                print(f"  {module}: 全部权限")
            else:
                actions_str = ", ".join(sorted(actions))
                print(f"  {module}: {actions_str}")
        print()

def identify_issues(users_without_area, no_role_users, roles):
    """识别潜在问题"""
    print("7. 潜在问题识别")
    print("-" * 40)

    issues = []

    # 检查无区域用户
    if users_without_area:
        issues.append(f"发现 {len(users_without_area)} 个用户没有关联区域")

    # 检查无角色用户
    if no_role_users:
        issues.append(f"发现 {len(no_role_users)} 个用户没有分配角色")

    # 检查权限配置异常
    for role in roles:
        try:
            permissions = parse_permissions_json(role.permissions)
        except:
            issues.append(f"角色 '{role.name}' 的权限配置格式异常")

    if issues:
        for issue in issues:
            print(f"  ⚠️  {issue}")
    else:
        print("  ✅ 未发现明显问题")
    print()

def provide_suggestions(users_without_area, no_role_users, multi_role_users, single_role_users):
    """提供优化建议"""
    print("8. 优化建议")
    print("-" * 40)

    suggestions = []

    if users_without_area:
        suggestions.append("为没有区域关联的用户分配合适的区域")

    if no_role_users:
        suggestions.append("为没有角色的用户分配合适的角色")

    if len(multi_role_users) > len(single_role_users):
        suggestions.append("考虑简化角色设计，减少多角色用户的复杂性")

    # 检查是否有过度权限
    admin_users = [user for user in User.query.all() if user.is_admin()]
    if len(admin_users) > 3:
        suggestions.append(f"系统管理员用户较多({len(admin_users)}个)，建议检查是否需要降级部分用户权限")

    if suggestions:
        for suggestion in suggestions:
            print(f"  💡 {suggestion}")
    else:
        print("  ✅ 当前配置较为合理")

if __name__ == '__main__':
    analyze_user_attributes()
