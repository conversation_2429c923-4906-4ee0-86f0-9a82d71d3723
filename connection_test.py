#!/usr/bin/env python3
"""
简单的连接测试
"""

import requests
import time

def test_connection():
    """测试基本连接"""
    print("🔍 测试应用连接...")
    
    try:
        # 简单的GET请求
        response = requests.get("http://localhost:5000", timeout=10)
        print(f"✅ 连接成功！状态码: {response.status_code}")
        
        # 检查安全头
        security_headers = ['X-Content-Type-Options', 'X-Frame-Options', 'X-XSS-Protection']
        print("\n🛡️ 安全头检查:")
        for header in security_headers:
            if header in response.headers:
                print(f"  ✅ {header}: {response.headers[header]}")
            else:
                print(f"  ❌ {header}: 未找到")
        
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败：无法连接到服务器")
        print("   请检查应用是否正在运行")
        return False
    except requests.exceptions.Timeout:
        print("❌ 连接超时")
        return False
    except Exception as e:
        print(f"❌ 连接错误: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始简单连接测试...\n")
    
    if test_connection():
        print("\n🎉 连接测试成功！")
        print("现在您可以安全地访问: http://localhost:5000")
        print("安全仪表盘: http://localhost:5000/security/dashboard")
    else:
        print("\n❌ 连接测试失败")
        print("请确保应用正在运行: python run.py")
