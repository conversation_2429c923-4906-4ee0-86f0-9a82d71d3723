"""
仪表盘API路由

提供仪表盘API接口，用于前端调用。
"""

from flask import Blueprint, jsonify, request
from flask_login import login_required
from app.services.dashboard_service import DashboardService
from app.models_daily_management import DiningCompanion, DailyLog
from app.models import MenuPlan
from sqlalchemy import desc
from datetime import date

# 创建蓝图
dashboard_api_bp = Blueprint('dashboard_api', __name__)

@dashboard_api_bp.route('/api/v2/dashboard/summary', methods=['GET'])
@login_required
def api_v2_dashboard_summary():
    """获取仪表盘摘要"""
    date_str = request.args.get('date')
    area_id = request.args.get('area_id', type=int)

    summary = DashboardService.get_dashboard_summary(date_str, area_id)
    return jsonify(summary)

@dashboard_api_bp.route('/api/v2/dashboard/weekly', methods=['GET'])
@login_required
def api_v2_weekly_summary():
    """获取周摘要"""
    week_start = request.args.get('week_start')
    area_id = request.args.get('area_id', type=int)

    summary = DashboardService.get_weekly_summary(week_start, area_id)
    return jsonify(summary)

@dashboard_api_bp.route('/api/v2/dashboard/monthly', methods=['GET'])
@login_required
def api_v2_monthly_summary():
    """获取月摘要"""
    year = request.args.get('year', type=int)
    month = request.args.get('month', type=int)
    area_id = request.args.get('area_id', type=int)

    summary = DashboardService.get_monthly_summary(year, month, area_id)
    return jsonify(summary)

@dashboard_api_bp.route('/api/v2/dining-companions/recent', methods=['GET'])
@login_required
def api_v2_recent_dining_companions():
    """获取最近的陪餐记录"""
    try:
        limit = request.args.get('limit', 5, type=int)

        # 直接查询陪餐记录
        companions = DiningCompanion.query.order_by(desc(DiningCompanion.dining_time)).limit(limit).all()

        result = []
        for companion in companions:
            log = DailyLog.query.get(companion.daily_log_id)

            record = {
                'id': companion.id,
                'name': companion.companion_name or '匿名',
                'role': companion.companion_role or '未知',
                'time': companion.dining_time.strftime('%H:%M') if companion.dining_time else '',
                'date': log.log_date.strftime('%Y-%m-%d') if log and log.log_date else '',
                'meal_type': companion.meal_type or '',
                'taste_rating': companion.taste_rating or 0,
                'hygiene_rating': companion.hygiene_rating or 0,
                'service_rating': companion.service_rating or 0
            }
            result.append(record)

        return jsonify(result)

    except Exception as e:
        return jsonify([]), 500

@dashboard_api_bp.route('/api/v2/dashboard/today-menu', methods=['GET'])
@login_required
def api_v2_today_menu():
    """获取今日菜单"""
    try:
        today_date = date.today()

        menu_data = {
            '早餐': {'recipes': [], 'status': '暂无菜单'},
            '午餐': {'recipes': [], 'status': '暂无菜单'},
            '晚餐': {'recipes': [], 'status': '暂无菜单'}
        }

        # 查询今日菜单计划
        plans = MenuPlan.query.filter_by(plan_date=today_date).all()

        for plan in plans:
            if plan.meal_type in menu_data:
                menu_data[plan.meal_type]['status'] = plan.status or '计划中'

                # 获取菜谱
                recipes = []
                for menu_recipe in plan.menu_recipes:
                    if menu_recipe.recipe:
                        recipes.append({
                            'name': menu_recipe.recipe.name,
                            'quantity': menu_recipe.planned_quantity
                        })

                menu_data[plan.meal_type]['recipes'] = recipes

        return jsonify({
            'success': True,
            'data': menu_data,
            'date': today_date.strftime('%Y-%m-%d')
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


