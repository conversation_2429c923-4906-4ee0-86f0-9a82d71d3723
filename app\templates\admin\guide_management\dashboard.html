{% extends "base.html" %}

{% block title %}新用户引导管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <h2><i class="fas fa-graduation-cap mr-2"></i>新用户引导管理</h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('system.index') }}">系统管理</a></li>
                    <li class="breadcrumb-item active">引导管理</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ stats.total_users }}</h4>
                            <p class="mb-0">总用户数</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ stats.guide_completed }}</h4>
                            <p class="mb-0">完成引导</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ "%.1f"|format(stats.completion_rate * 100) }}%</h4>
                            <p class="mb-0">完成率</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ stats.new_users_today }}</h4>
                            <p class="mb-0">今日新用户</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-plus fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 功能导航 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-cogs mr-2"></i>管理功能</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('guide_management.user_guide_status') }}" class="btn btn-outline-primary btn-block">
                                <i class="fas fa-users fa-2x d-block mb-2"></i>
                                <strong>用户引导状态</strong>
                                <small class="d-block text-muted">查看和管理用户引导进度</small>
                            </a>
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('guide_management.scenario_management') }}" class="btn btn-outline-info btn-block">
                                <i class="fas fa-school fa-2x d-block mb-2"></i>
                                <strong>场景管理</strong>
                                <small class="d-block text-muted">管理不同学校类型的引导场景</small>
                            </a>
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('guide_management.video_management') }}" class="btn btn-outline-success btn-block">
                                <i class="fas fa-video fa-2x d-block mb-2"></i>
                                <strong>视频管理</strong>
                                <small class="d-block text-muted">上传和管理引导视频资源</small>
                            </a>
                        </div>
                        
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('guide_management.demo_data_management') }}" class="btn btn-outline-warning btn-block">
                                <i class="fas fa-database fa-2x d-block mb-2"></i>
                                <strong>演示数据</strong>
                                <small class="d-block text-muted">管理和清理演示数据</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 详细统计和最近活动 -->
    <div class="row">
        <!-- 引导步骤统计 -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-bar mr-2"></i>引导步骤统计</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>平均完成时间</span>
                            <strong>{{ stats.average_time }}</strong>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>最受欢迎步骤</span>
                            <strong>周菜单制定</strong>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>最容易放弃步骤</span>
                            <strong>采购订单</strong>
                        </div>
                    </div>
                    
                    <!-- 进度条示例 -->
                    <div class="mt-4">
                        <h6>各步骤完成率</h6>
                        <div class="mb-2">
                            <div class="d-flex justify-content-between">
                                <small>欢迎介绍</small>
                                <small>95%</small>
                            </div>
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar bg-success" style="width: 95%"></div>
                            </div>
                        </div>
                        
                        <div class="mb-2">
                            <div class="d-flex justify-content-between">
                                <small>日常管理</small>
                                <small>87%</small>
                            </div>
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar bg-info" style="width: 87%"></div>
                            </div>
                        </div>
                        
                        <div class="mb-2">
                            <div class="d-flex justify-content-between">
                                <small>周菜单制定</small>
                                <small>78%</small>
                            </div>
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar bg-primary" style="width: 78%"></div>
                            </div>
                        </div>
                        
                        <div class="mb-2">
                            <div class="d-flex justify-content-between">
                                <small>采购订单</small>
                                <small>65%</small>
                            </div>
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar bg-warning" style="width: 65%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最近活动 -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-clock mr-2"></i>最近引导活动</h5>
                </div>
                <div class="card-body">
                    {% if recent_activities %}
                        <div class="timeline">
                            {% for activity in recent_activities[:8] %}
                            <div class="timeline-item">
                                <div class="timeline-marker bg-primary"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">{{ activity.user.real_name or activity.user.username }}</h6>
                                    <p class="mb-1 text-muted">{{ activity.action }}</p>
                                    <small class="text-muted">
                                        {{ activity.time.strftime('%Y-%m-%d %H:%M') }}
                                        {% if activity.school_type != 'unknown' %}
                                        | {{ activity.school_type }}
                                        {% endif %}
                                    </small>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center text-muted">
                            <i class="fas fa-inbox fa-3x mb-3"></i>
                            <p>暂无最近活动</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 视频资源统计 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-video mr-2"></i>视频资源统计</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-primary">{{ video_stats.total_videos }}</h4>
                                <p class="mb-0">总视频数</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-success">{{ video_stats.total_duration }}</h4>
                                <p class="mb-0">总时长</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-info">{{ video_stats.most_watched }}</h4>
                                <p class="mb-0">最受欢迎</p>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <h4 class="text-warning">{{ "%.0f"|format(video_stats.completion_rate * 100) }}%</h4>
                                <p class="mb-0">观看完成率</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -31px;
    top: 15px;
    width: 2px;
    height: calc(100% + 5px);
    background-color: #dee2e6;
}

.btn-block {
    height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-decoration: none;
}

.btn-block:hover {
    text-decoration: none;
}
</style>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
$(document).ready(function() {
    // 自动刷新统计数据
    setInterval(function() {
        // 这里可以添加AJAX刷新逻辑
    }, 30000); // 30秒刷新一次
});
</script>
{% endblock %}
