#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
学校注册服务
处理用户注册时自动创建学校和分配权限的逻辑
"""

import uuid
import json
from datetime import datetime
from sqlalchemy import text
from app import db
from app.models import User, Role, UserRole, AdministrativeArea, Warehouse, StorageLocation


class SchoolRegistrationService:
    """学校注册服务类"""

    @staticmethod
    def register_school_and_user(form_data):
        """
        注册学校和用户

        Args:
            form_data: 包含学校名称和用户信息的字典

        Returns:
            tuple: (user, school_area) 创建的用户和学校区域对象

        Raises:
            ValueError: 当创建失败时抛出异常
        """
        try:
            # 1. 创建学校区域
            school_area = SchoolRegistrationService._create_school_area(
                form_data['school_name']
            )

            # 2. 创建用户
            user = SchoolRegistrationService._create_user(
                form_data, school_area.id
            )

            # 3. 分配学校管理员角色
            SchoolRegistrationService._assign_school_admin_role(user)

            # 4. 创建默认仓库（直接关联到学校区域）
            warehouse = SchoolRegistrationService._create_default_warehouse(
                school_area, user
            )

            # 5. 创建默认储存位置
            SchoolRegistrationService._create_default_storage_locations(warehouse)

            # 提交事务
            db.session.commit()

            return user, school_area

        except Exception as e:
            # 回滚事务
            db.session.rollback()
            raise ValueError(f"注册失败: {str(e)}")

    @staticmethod
    def _create_school_area(school_name):
        """
        创建学校区域

        Args:
            school_name: 学校名称

        Returns:
            AdministrativeArea: 创建的学校区域对象
        """
        # 生成唯一的学校代码
        school_code = SchoolRegistrationService._generate_school_code(school_name)

        # 使用原始SQL创建学校区域，避免时间精度问题
        # 使用数据库函数 GETDATE() 处理时间字段，确保精度一致性
        sql = text('''
        INSERT INTO administrative_areas
        (name, code, level, parent_id, description, status, is_township_school, created_at)
        OUTPUT inserted.id
        VALUES
        (:name, :code, :level, :parent_id, :description, :status, :is_township_school, GETDATE())
        ''')

        result = db.session.execute(sql, {
            'name': school_name,
            'code': school_code,
            'level': 3,  # 学校级别
            'parent_id': None,  # 独立学校，无上级区域
            'description': f'通过开放注册创建的学校：{school_name}',
            'status': 1,  # 启用状态
            'is_township_school': False
        })

        # 获取新创建的区域ID
        area_id = result.scalar()

        # 返回区域对象
        return AdministrativeArea.query.get(area_id)

    @staticmethod
    def _create_user(form_data, area_id):
        """
        创建用户

        Args:
            form_data: 表单数据
            area_id: 学校区域ID

        Returns:
            User: 创建的用户对象
        """
        # 使用原始SQL创建用户，避免时间精度问题
        # 使用数据库函数 GETDATE() 处理时间字段，确保精度一致性
        sql = text('''
        INSERT INTO users
        (username, password_hash, email, real_name, phone, status, area_id, area_level, created_at)
        OUTPUT inserted.id
        VALUES
        (:username, :password_hash, :email, :real_name, :phone, :status, :area_id, :area_level, GETDATE())
        ''')

        # 创建临时用户对象来生成密码哈希
        temp_user = User()
        temp_user.set_password(form_data['password'])

        result = db.session.execute(sql, {
            'username': form_data['username'],
            'password_hash': temp_user.password_hash,
            'email': form_data['email'],
            'real_name': form_data['real_name'],
            'phone': form_data['phone'],
            'status': 1,  # 启用状态
            'area_id': area_id,
            'area_level': 3  # 学校级别
        })

        # 获取新创建的用户ID
        user_id = result.scalar()

        # 返回用户对象
        return User.query.get(user_id)

    @staticmethod
    def _assign_school_admin_role(user):
        """
        为用户分配学校管理员角色

        Args:
            user: 用户对象
        """
        # 查找或创建学校管理员角色
        school_admin_role = Role.query.filter_by(name='学校管理员').first()

        if not school_admin_role:
            # 如果不存在学校管理员角色，创建一个
            school_admin_role = SchoolRegistrationService._create_school_admin_role()

        # 分配角色
        user_role = UserRole(user_id=user.id, role_id=school_admin_role.id)
        db.session.add(user_role)

    @staticmethod
    def _create_school_admin_role():
        """
        创建学校管理员角色

        Returns:
            Role: 创建的角色对象
        """
        # 学校管理员的权限配置
        permissions = {
            "*": ["*"]  # 给予所有权限，让用户感觉这是他们的专用系统
        }

        # 使用原始SQL创建角色
        # 使用数据库函数 GETDATE() 处理时间字段，确保精度一致性
        sql = text('''
        INSERT INTO roles
        (name, description, permissions, created_at)
        OUTPUT inserted.id
        VALUES
        (:name, :description, :permissions, GETDATE())
        ''')

        result = db.session.execute(sql, {
            'name': '学校管理员',
            'description': '学校管理员，负责管理学校信息',
            'permissions': json.dumps(permissions)  # 转换为JSON格式
        })

        # 获取新创建的角色ID
        role_id = result.scalar()

        # 返回角色对象
        return Role.query.get(role_id)

    # 注释：不再创建默认食堂区域，直接使用学校区域
    # @staticmethod
    # def _create_default_canteen(school_area):
    #     """
    #     为学校创建默认食堂区域（已废弃）
    #     现在直接使用学校区域，不再创建额外的食堂区域
    #     """
    #     pass

    @staticmethod
    def _create_default_warehouse(school_area, user):
        """
        为学校创建默认仓库

        Args:
            school_area: 学校区域对象
            user: 用户对象（作为仓库管理员）

        Returns:
            Warehouse: 创建的仓库对象
        """
        # 使用原始SQL创建仓库
        # 使用数据库函数 GETDATE() 处理时间字段，确保精度一致性
        sql = text('''
        INSERT INTO warehouses
        (name, area_id, location, manager_id, capacity, capacity_unit,
         temperature_range, humidity_range, status, notes, created_at, updated_at)
        OUTPUT inserted.id
        VALUES
        (:name, :area_id, :location, :manager_id, :capacity, :capacity_unit,
         :temperature_range, :humidity_range, :status, :notes, GETDATE(), GETDATE())
        ''')

        result = db.session.execute(sql, {
            'name': f'{school_area.name}中心仓库',
            'area_id': school_area.id,
            'location': '食堂一楼',
            'manager_id': user.id,
            'capacity': 500.0,
            'capacity_unit': '立方米',
            'temperature_range': '-18°C ~ 25°C',
            'humidity_range': '45% ~ 65%',
            'status': '正常',
            'notes': '学校默认仓库，包含常温、冷藏、冷冻三个储存区域'
        })

        # 获取新创建的仓库ID
        warehouse_id = result.scalar()

        # 返回仓库对象
        return Warehouse.query.get(warehouse_id)

    @staticmethod
    def _create_default_storage_locations(warehouse):
        """
        为仓库创建默认储存位置

        Args:
            warehouse: 仓库对象
        """
        # 定义三个基本储存位置的配置
        storage_configs = [
            {
                'name': '储存室',
                'location_code': 'A001',
                'storage_type': '常温',
                'capacity': 200.0,
                'capacity_unit': '立方米',
                'temperature_range': '15°C ~ 25°C',
                'notes': '常温储存区域，适合储存干货、调料等常温食材'
            },
            {
                'name': '冷藏区',
                'location_code': 'B001',
                'storage_type': '冷藏',
                'capacity': 150.0,
                'capacity_unit': '立方米',
                'temperature_range': '0°C ~ 4°C',
                'notes': '冷藏储存区域，适合储存蔬菜、水果、乳制品等需要冷藏的食材'
            },
            {
                'name': '冷冻区',
                'location_code': 'C001',
                'storage_type': '冷冻',
                'capacity': 150.0,
                'capacity_unit': '立方米',
                'temperature_range': '-18°C ~ -12°C',
                'notes': '冷冻储存区域，适合储存肉类、海鲜、冷冻食品等需要冷冻的食材'
            }
        ]

        # 使用原始SQL创建储存位置
        sql = text('''
        INSERT INTO storage_locations
        (warehouse_id, name, location_code, storage_type, capacity, capacity_unit,
         temperature_range, status, notes, created_at, updated_at)
        VALUES
        (:warehouse_id, :name, :location_code, :storage_type, :capacity, :capacity_unit,
         :temperature_range, :status, :notes, GETDATE(), GETDATE())
        ''')

        # 批量创建储存位置
        for config in storage_configs:
            db.session.execute(sql, {
                'warehouse_id': warehouse.id,
                'name': config['name'],
                'location_code': config['location_code'],
                'storage_type': config['storage_type'],
                'capacity': config['capacity'],
                'capacity_unit': config['capacity_unit'],
                'temperature_range': config['temperature_range'],
                'status': '正常',
                'notes': config['notes']
            })

    @staticmethod
    def _generate_school_code(school_name):
        """
        生成唯一的学校代码

        Args:
            school_name: 学校名称

        Returns:
            str: 学校代码
        """
        # 使用时间戳和UUID生成唯一代码
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        unique_id = str(uuid.uuid4())[:8].upper()

        # 取学校名称的前两个字符作为前缀
        name_prefix = school_name[:2] if len(school_name) >= 2 else school_name

        return f"SCHOOL_{name_prefix}_{timestamp}_{unique_id}"

    @staticmethod
    def check_school_name_exists(school_name):
        """
        检查学校名称是否已存在

        Args:
            school_name: 学校名称

        Returns:
            bool: 如果存在返回True，否则返回False
        """
        existing_school = AdministrativeArea.query.filter_by(
            name=school_name,
            level=3  # 学校级别
        ).first()

        return existing_school is not None
