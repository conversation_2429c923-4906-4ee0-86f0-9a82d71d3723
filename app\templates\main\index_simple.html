<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>校园餐智慧食堂平台 - 智能化食堂管理解决方案</title>
  
  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Font Awesome -->
  <link href="{{ url_for('static', filename='css/font-awesome.min.css') }}" rel="stylesheet">
  <!-- Chart.js -->
  <script src="{{ url_for('static', filename='js/chart.umd.min.js') }}"></script>

  <style>
    /* 深色主题 */
    body {
      background: linear-gradient(135deg, #0B0E2F 0%, #1D2129 100%);
      color: white;
      font-family: 'Inter', system-ui, sans-serif;
      min-height: 100vh;
    }
    
    /* 霓虹灯效果 */
    .neon-glow {
      box-shadow: 0 0 10px rgba(0, 191, 255, 0.5), 0 0 20px rgba(0, 191, 255, 0.3);
    }
    .neon-text {
      color: #00BFFF;
      text-shadow: 0 0 10px rgba(0, 191, 255, 0.7);
    }
    
    /* 动画效果 */
    .animate-float {
      animation: float 6s ease-in-out infinite;
    }
    @keyframes float {
      0% { transform: translateY(0px); }
      50% { transform: translateY(-20px); }
      100% { transform: translateY(0px); }
    }
    
    .data-pulse {
      animation: pulse 2s infinite;
    }
    @keyframes pulse {
      0% { transform: scale(1); opacity: 1; }
      50% { transform: scale(1.05); opacity: 0.8; }
      100% { transform: scale(1); opacity: 1; }
    }
    
    /* 卡片样式 */
    .card-dark {
      background: rgba(29, 33, 41, 0.3);
      backdrop-filter: blur(12px);
      border: 1px solid rgba(22, 93, 255, 0.2);
      transition: all 0.3s ease;
    }
    .card-dark:hover {
      transform: translateY(-5px);
      box-shadow: 0 10px 25px rgba(0, 191, 255, 0.2);
    }
    
    /* 背景网格 */
    .bg-grid {
      background-image: 
        linear-gradient(rgba(22, 93, 255, 0.05) 1px, transparent 1px),
        linear-gradient(90deg, rgba(22, 93, 255, 0.05) 1px, transparent 1px);
      background-size: 20px 20px;
    }
    
    /* 导航栏 */
    .navbar-dark {
      background: rgba(11, 14, 47, 0.8) !important;
      backdrop-filter: blur(12px);
      border-bottom: 1px solid rgba(22, 93, 255, 0.2);
    }
    
    /* 按钮样式 */
    .btn-neon {
      background: linear-gradient(45deg, #165DFF, #00BFFF);
      border: none;
      color: white;
      font-weight: 600;
      transition: all 0.3s ease;
    }
    .btn-neon:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(0, 191, 255, 0.4);
      color: white;
    }
    
    .btn-outline-neon {
      border: 2px solid #00BFFF;
      color: #00BFFF;
      background: transparent;
      font-weight: 600;
      transition: all 0.3s ease;
    }
    .btn-outline-neon:hover {
      background: #00BFFF;
      color: white;
      transform: translateY(-2px);
    }
    
    /* 浮动装饰 */
    .floating-decoration {
      position: absolute;
      border-radius: 50%;
      filter: blur(60px);
      opacity: 0.3;
      animation: float 8s ease-in-out infinite;
    }
    .decoration-1 {
      width: 300px;
      height: 300px;
      background: rgba(0, 191, 255, 0.2);
      top: -150px;
      left: -150px;
    }
    .decoration-2 {
      width: 400px;
      height: 400px;
      background: rgba(157, 78, 221, 0.2);
      bottom: -200px;
      right: -200px;
      animation-delay: -3s;
    }
    .decoration-3 {
      width: 250px;
      height: 250px;
      background: rgba(0, 255, 157, 0.2);
      top: 25%;
      left: -125px;
      animation-delay: -1.5s;
    }
    
    /* 响应式文本 */
    .hero-title {
      font-size: clamp(2rem, 5vw, 3.5rem);
      font-weight: 700;
      line-height: 1.2;
    }
    .hero-subtitle {
      font-size: clamp(1.8rem, 4vw, 2.8rem);
      font-weight: 700;
      line-height: 1.2;
    }
    
    /* 数据指标 */
    .metric-card {
      background: rgba(29, 33, 41, 0.3);
      backdrop-filter: blur(8px);
      border: 1px solid rgba(22, 93, 255, 0.2);
      border-radius: 12px;
      padding: 1.5rem;
      text-align: center;
    }
    .metric-value {
      font-size: 2rem;
      font-weight: 700;
      font-family: 'JetBrains Mono', monospace;
    }
    .metric-label {
      font-size: 0.875rem;
      color: #9ca3af;
    }
    
    /* 功能卡片 */
    .feature-icon {
      width: 60px;
      height: 60px;
      background: rgba(22, 93, 255, 0.2);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 1.5rem;
      transition: all 0.3s ease;
    }
    .feature-icon:hover {
      background: rgba(22, 93, 255, 0.3);
    }
    
    /* 图表容器 */
    .chart-container {
      background: rgba(29, 33, 41, 0.3);
      border: 1px solid rgba(22, 93, 255, 0.2);
      border-radius: 12px;
      padding: 1.5rem;
      height: 250px;
    }
    
    /* 自定义滚动条 */
    ::-webkit-scrollbar {
      width: 8px;
    }
    ::-webkit-scrollbar-track {
      background: rgba(29, 33, 41, 0.3);
    }
    ::-webkit-scrollbar-thumb {
      background: rgba(0, 191, 255, 0.5);
      border-radius: 4px;
    }
    ::-webkit-scrollbar-thumb:hover {
      background: rgba(0, 191, 255, 0.7);
    }
  </style>
</head>

<body class="bg-grid">
  <!-- 浮动装饰元素 -->
  <div class="floating-decoration decoration-1"></div>
  <div class="floating-decoration decoration-2"></div>
  <div class="floating-decoration decoration-3"></div>

  <!-- 导航栏 -->
  <nav class="navbar navbar-expand-lg navbar-dark fixed-top">
    <div class="container">
      <a class="navbar-brand d-flex align-items-center" href="#">
        <div class="bg-primary rounded p-2 me-2 neon-glow">
          <i class="fas fa-utensils text-white"></i>
        </div>
        <span class="fw-bold">智慧食堂<span class="neon-text">平台</span></span>
      </a>
      
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
      </button>
      
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav ms-auto">
          <li class="nav-item">
            <a class="nav-link" href="#features">核心功能</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#advantages">系统优势</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#process">管理流程</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="#contact">联系我们</a>
          </li>
        </ul>
      </div>
    </div>
  </nav>

  <!-- 英雄区域 -->
  <section class="py-5" style="margin-top: 80px; min-height: 100vh;">
    <div class="container">
      <div class="row align-items-center min-vh-100">
        <div class="col-lg-6 mb-5 mb-lg-0">
          <h1 class="hero-title mb-4">
            智慧食堂管理平台<br>
            <span class="neon-text">全方位智能化解决方案</span>
          </h1>
          <p class="lead mb-4 text-light">
            致力于打造全方位智能化管理体系，实现食品安全可视化、可管控、可追溯，为校园食堂管理提供高效便捷的技术支持。
          </p>
          <div class="d-flex flex-wrap gap-3 mb-5">
            <a href="#features" class="btn btn-neon px-4 py-2">
              了解核心功能 <i class="fas fa-arrow-right ms-2"></i>
            </a>
            <a href="#contact" class="btn btn-outline-neon px-4 py-2">
              联系我们 <i class="fas fa-envelope ms-2"></i>
            </a>
          </div>
          
          <!-- 数据指标 -->
          <div class="row g-3">
            <div class="col-4">
              <div class="metric-card">
                <div class="metric-value neon-text data-pulse">99.9%</div>
                <div class="metric-label">系统稳定性</div>
              </div>
            </div>
            <div class="col-4">
              <div class="metric-card">
                <div class="metric-value text-success data-pulse">80%</div>
                <div class="metric-label">管理效率提升</div>
              </div>
            </div>
            <div class="col-4">
              <div class="metric-card">
                <div class="metric-value text-warning data-pulse">100%</div>
                <div class="metric-label">食品溯源率</div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="col-lg-6">
          <div class="animate-float">
            <div class="card-dark rounded-3 overflow-hidden">
              <img src="{{ url_for('static', filename='images/dashboard.jpg') }}" alt="智慧食堂管理系统界面" class="img-fluid">
              <div class="p-3 border-top" style="border-color: rgba(22, 93, 255, 0.2) !important;">
                <div class="d-flex justify-content-between align-items-center">
                  <div class="d-flex align-items-center">
                    <div class="bg-success bg-opacity-25 rounded-circle p-2 me-3">
                      <i class="fas fa-check text-success"></i>
                    </div>
                    <div>
                      <div class="fw-medium">食品安全</div>
                      <small class="text-muted">全程可追溯</small>
                    </div>
                  </div>
                  <small class="text-muted">
                    实时监控中 <span class="badge bg-success rounded-pill ms-1"></span>
                  </small>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</body>
</html>
