// 智慧食堂现代化交互脚本

document.addEventListener('DOMContentLoaded', function() {
    // 初始化现代化导航栏
    initSmartNavbar();
    
    // 初始化滚动效果
    initScrollEffects();
    
    // 初始化主题切换
    initThemeSwitcher();
    
    // 初始化通知系统
    initNotificationSystem();
    
    // 初始化移动端菜单
    initMobileMenu();
});

// 初始化现代化导航栏
function initSmartNavbar() {
    const navbar = document.querySelector('.navbar');
    if (navbar) {
        navbar.classList.add('smart-navbar');
        
        // 添加活动状态到当前页面的导航链接
        const currentPath = window.location.pathname;
        const navLinks = navbar.querySelectorAll('.nav-link');
        
        navLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href && currentPath.includes(href) && href !== '/') {
                link.classList.add('active');
            }
        });
    }
}

// 初始化滚动效果
function initScrollEffects() {
    const navbar = document.querySelector('.smart-navbar');
    
    if (navbar) {
        window.addEventListener('scroll', function() {
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });
    }
    
    // 平滑滚动到顶部
    const scrollToTopBtn = createScrollToTopButton();
    document.body.appendChild(scrollToTopBtn);
    
    window.addEventListener('scroll', function() {
        if (window.scrollY > 300) {
            scrollToTopBtn.style.display = 'flex';
        } else {
            scrollToTopBtn.style.display = 'none';
        }
    });
}

// 创建回到顶部按钮
function createScrollToTopButton() {
    const button = document.createElement('button');
    button.innerHTML = '<i class="fas fa-arrow-up"></i>';
    button.className = 'scroll-to-top-btn';
    button.style.cssText = `
        position: fixed;
        bottom: 30px;
        right: 30px;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        display: none;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        z-index: 1000;
        box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
        transition: all 0.3s ease;
    `;
    
    button.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-3px) scale(1.1)';
        this.style.boxShadow = '0 8px 30px rgba(102, 126, 234, 0.6)';
    });
    
    button.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
        this.style.boxShadow = '0 4px 20px rgba(102, 126, 234, 0.4)';
    });
    
    button.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
    
    return button;
}

// 初始化主题切换
function initThemeSwitcher() {
    const themeOptions = document.querySelectorAll('.theme-option');
    
    themeOptions.forEach(option => {
        option.addEventListener('click', function(e) {
            e.preventDefault();
            const theme = this.getAttribute('data-theme');
            switchTheme(theme);
        });
    });
}

// 切换主题
function switchTheme(theme) {
    document.body.setAttribute('data-theme', theme);
    
    // 保存主题到本地存储
    localStorage.setItem('smart-canteen-theme', theme);
    
    // 更新导航栏颜色
    updateNavbarTheme(theme);
    
    // 显示切换成功提示
    showNotification('主题已切换', 'success');
}

// 更新导航栏主题
function updateNavbarTheme(theme) {
    const navbar = document.querySelector('.smart-navbar');
    if (!navbar) return;
    
    const themeColors = {
        primary: 'linear-gradient(135deg, #007bff 0%, #0056b3 100%)',
        secondary: 'linear-gradient(135deg, #6c757d 0%, #495057 100%)',
        success: 'linear-gradient(135deg, #28a745 0%, #1e7e34 100%)',
        warning: 'linear-gradient(135deg, #ffc107 0%, #e0a800 100%)',
        info: 'linear-gradient(135deg, #17a2b8 0%, #117a8b 100%)',
        danger: 'linear-gradient(135deg, #dc3545 0%, #bd2130 100%)',
        dark: 'linear-gradient(135deg, #343a40 0%, #1d2124 100%)',
        default: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    };
    
    const gradient = themeColors[theme] || themeColors.default;
    navbar.style.background = gradient;
}

// 初始化通知系统
function initNotificationSystem() {
    // 检查新通知
    checkNewNotifications();
    
    // 每30秒检查一次新通知
    setInterval(checkNewNotifications, 30000);
    
    // 处理通知点击
    const notificationItems = document.querySelectorAll('.notification-item');
    notificationItems.forEach(item => {
        item.addEventListener('click', function() {
            this.classList.remove('unread');
            updateNotificationBadge();
        });
    });
}

// 检查新通知
function checkNewNotifications() {
    // 这里可以添加AJAX请求来获取新通知
    // 暂时使用模拟数据
}

// 更新通知徽章
function updateNotificationBadge() {
    const badge = document.getElementById('notification-badge');
    const unreadItems = document.querySelectorAll('.notification-item.unread');
    
    if (badge) {
        if (unreadItems.length > 0) {
            badge.textContent = unreadItems.length;
            badge.style.display = 'flex';
        } else {
            badge.style.display = 'none';
        }
    }
}

// 初始化移动端菜单
function initMobileMenu() {
    const navbarToggler = document.querySelector('.navbar-toggler');
    const navbarCollapse = document.querySelector('.navbar-collapse');
    
    if (navbarToggler && navbarCollapse) {
        navbarToggler.addEventListener('click', function() {
            const isExpanded = this.getAttribute('aria-expanded') === 'true';
            
            if (!isExpanded) {
                // 添加展开动画
                navbarCollapse.style.animation = 'slideDown 0.3s ease';
            } else {
                // 添加收起动画
                navbarCollapse.style.animation = 'slideUp 0.3s ease';
            }
        });
    }
}

// 显示通知
function showNotification(message, type = 'info') {
    // 如果存在toastr，使用toastr
    if (typeof toastr !== 'undefined') {
        toastr[type](message);
        return;
    }
    
    // 否则创建自定义通知
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        animation: slideInRight 0.3s ease;
    `;
    
    notification.innerHTML = `
        ${message}
        <button type="button" class="close" onclick="this.parentElement.remove()">
            <span>&times;</span>
        </button>
    `;
    
    document.body.appendChild(notification);
    
    // 3秒后自动移除
    setTimeout(() => {
        if (notification.parentElement) {
            notification.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => notification.remove(), 300);
        }
    }, 3000);
}

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideDown {
        from { opacity: 0; transform: translateY(-20px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    @keyframes slideUp {
        from { opacity: 1; transform: translateY(0); }
        to { opacity: 0; transform: translateY(-20px); }
    }
    
    @keyframes slideInRight {
        from { opacity: 0; transform: translateX(100%); }
        to { opacity: 1; transform: translateX(0); }
    }
    
    @keyframes slideOutRight {
        from { opacity: 1; transform: translateX(0); }
        to { opacity: 0; transform: translateX(100%); }
    }
`;
document.head.appendChild(style);

// 加载保存的主题
document.addEventListener('DOMContentLoaded', function() {
    const savedTheme = localStorage.getItem('smart-canteen-theme');
    if (savedTheme) {
        switchTheme(savedTheme);
    }
});
