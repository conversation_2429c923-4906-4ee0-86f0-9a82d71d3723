from flask_wtf import FlaskForm
from wtforms import StringField, PasswordField, SelectField, SelectMultipleField, SubmitField, BooleanField, FieldList, FormField
from wtforms.validators import DataRequired, Email, EqualTo, Length, ValidationError, Optional
from app.models import User, Role, AdministrativeArea
from flask_login import current_user

class SchoolUserForm(FlaskForm):
    """学校用户表单"""
    username = StringField('用户名', validators=[DataRequired(message='请输入用户名'), Length(min=3, max=20, message='用户名长度必须在3-20个字符之间')])
    email = StringField('电子邮箱', validators=[DataRequired(message='请输入电子邮箱'), Email(message='请输入有效的电子邮箱地址')])
    real_name = StringField('真实姓名', validators=[DataRequired(message='请输入真实姓名'), Length(min=2, max=20, message='姓名长度必须在2-20个字符之间')])
    phone = StringField('手机号码', validators=[DataRequired(message='请输入手机号码'), Length(min=11, max=11, message='请输入11位手机号码')])
    password = PasswordField('密码', validators=[Optional(), Length(min=6, message='密码长度不能少于6个字符')])
    password2 = PasswordField('确认密码', validators=[EqualTo('password', message='两次输入的密码不一致')])
    # 使用隐藏字段存储选中的角色ID
    roles = SelectMultipleField('角色', coerce=int, validators=[DataRequired(message='请选择至少一个角色')])
    # 移除子区域选择，直接使用学校区域
    # sub_area_id = SelectField('所属子区域', coerce=int, validators=[Optional()])
    status = SelectField('状态', choices=[(1, '启用'), (0, '禁用')], coerce=int, validators=[DataRequired(message='请选择状态')])
    submit = SubmitField('提交')

    # 用于存储用户ID，用于编辑时验证唯一性
    user_id = None

    def __init__(self, *args, **kwargs):
        super(SchoolUserForm, self).__init__(*args, **kwargs)

        # 直接使用学校区域，不再需要子区域选择
        if current_user.is_authenticated and current_user.area:
            # 获取可用角色（排除系统管理员和超级管理员）
            available_roles = Role.query.filter(~Role.name.in_(['系统管理员', '超级管理员'])).all()
            self.roles.choices = [(role.id, role.name) for role in available_roles]

    def validate_username(self, username):
        """验证用户名唯一性"""
        user = User.query.filter_by(username=username.data).first()
        if user and (not hasattr(self, 'user_id') or user.id != self.user_id):
            raise ValidationError('该用户名已被使用，请更换一个')

    def validate_email(self, email):
        """验证邮箱唯一性"""
        user = User.query.filter_by(email=email.data).first()
        if user and (not hasattr(self, 'user_id') or user.id != self.user_id):
            raise ValidationError('该邮箱已被注册，请更换一个')

    def validate_password2(self, password2):
        """验证两次密码输入是否一致"""
        if self.password.data and self.password.data != password2.data:
            raise ValidationError('两次输入的密码不一致')
