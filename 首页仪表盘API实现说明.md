# 首页仪表盘API实现说明

## 概述

为首页仪表盘创建了专门的API接口，用于直接从数据库读取今日菜单和陪餐记录数据，提高页面加载性能和数据实时性。

## 实现的功能

### 1. 今日菜单API

**接口地址**: `/api/v2/dashboard/today-menu`
**请求方法**: GET
**功能**: 获取当前用户可访问区域的今日菜单信息

#### 数据来源
1. **菜单计划表** (`menu_plans`) - 优先获取今日的菜单计划
2. **周菜单表** (`weekly_menus`) - 如果没有菜单计划，则从周菜单获取

#### 返回数据格式
```json
{
    "success": true,
    "data": {
        "早餐": {
            "recipes": [
                {
                    "id": 1,
                    "name": "小笼包",
                    "quantity": 100
                }
            ],
            "status": "已发布",
            "expected_diners": 200
        },
        "午餐": { ... },
        "晚餐": { ... }
    },
    "date": "2024-01-15"
}
```

### 2. 陪餐记录API

**接口地址**: `/api/v2/dining-companions/recent`
**请求方法**: GET
**参数**: 
- `limit`: 返回记录数量，默认5条

#### 数据来源
直接查询数据库表：
- `dining_companions` - 陪餐记录表
- `daily_logs` - 日志表（关联区域信息）
- `areas` - 区域表
- `photos` - 照片表（检查是否有照片）

#### 返回数据格式
```json
[
    {
        "id": 1,
        "name": "张校长",
        "role": "校长",
        "time": "12:30",
        "date": "2024-01-15",
        "meal_type": "午餐",
        "comments": "菜品质量很好",
        "has_photo": true,
        "area_name": "主校区食堂",
        "taste_rating": 5,
        "hygiene_rating": 5,
        "service_rating": 4
    }
]
```

## 技术实现

### 1. 数据库查询优化

使用原生SQL查询，提高查询效率：

```python
# 今日菜单查询
sql = text(f"""
    SELECT 
        mp.meal_type,
        mp.status,
        mp.expected_diners,
        r.id as recipe_id,
        r.name as recipe_name,
        mr.planned_quantity
    FROM menu_plans mp
    LEFT JOIN menu_recipes mr ON mp.id = mr.menu_plan_id
    LEFT JOIN recipes r ON mr.recipe_id = r.id
    WHERE mp.plan_date = '{today_date.strftime('%Y-%m-%d')}'
    AND mp.area_id IN {area_ids_str}
    ORDER BY mp.meal_type, r.name
""")
```

### 2. 权限控制

- 基于用户可访问的区域进行数据过滤
- 确保用户只能看到自己权限范围内的数据

### 3. 前端集成

#### JavaScript异步加载
```javascript
// 加载今日菜单
function loadTodayMenu() {
    $.ajax({
        url: '/api/v2/dashboard/today-menu',
        type: 'GET',
        success: function(response) {
            if (response.success && response.data) {
                updateMenuDisplay(response.data);
            }
        }
    });
}

// 加载陪餐记录
function loadRecentCompanions() {
    $.ajax({
        url: '/api/v2/dining-companions/recent',
        type: 'GET',
        data: { limit: 5 },
        success: function(data) {
            updateCompanionsDisplay(data);
        }
    });
}
```

## 文件修改清单

### 1. 新增/修改的文件

1. **`app/routes/dashboard_api.py`**
   - 添加了今日菜单API接口
   - 完善了陪餐记录API接口

2. **`app/templates/main/canteen_dashboard_new.html`**
   - 更新了菜单显示区域，添加加载状态
   - 添加了JavaScript函数处理API数据
   - 实现了动态更新菜单和陪餐记录显示

3. **`app/main/routes.py`**
   - 简化了首页路由，移除了数据库查询逻辑
   - 数据获取改为通过API异步加载

### 2. 蓝图注册

dashboard_api蓝图已在`app/__init__.py`中注册（第282-283行）

## 优势

1. **性能提升**: 页面首次加载更快，数据异步获取
2. **实时性**: 通过API可以实时刷新数据
3. **可维护性**: API接口独立，便于测试和维护
4. **扩展性**: 可以轻松添加更多仪表盘数据接口

## 使用说明

1. 页面加载时会自动调用API获取数据
2. 可以通过刷新按钮重新加载数据
3. 所有数据都基于用户权限进行过滤
4. 支持错误处理和加载状态显示

## 注意事项

1. API接口需要用户登录才能访问
2. 数据查询基于用户可访问的区域
3. 使用了原生SQL查询，需要注意SQL注入防护
4. 前端需要处理API调用失败的情况
