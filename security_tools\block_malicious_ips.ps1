# Windows防火墙IP阻止脚本
# 用于阻止恶意IP地址访问

param(
    [Parameter(Mandatory=$false)]
    [string[]]$IPAddresses,
    
    [Parameter(Mandatory=$false)]
    [string]$Action = "Block",  # Block 或 Unblock
    
    [Parameter(Mandatory=$false)]
    [string]$RuleName = "Block_Malicious_IPs"
)

# 检查是否以管理员身份运行
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Error "此脚本需要管理员权限运行"
    exit 1
}

# 已知的恶意IP列表（基于日志分析）
$KnownMaliciousIPs = @(
    "***************",
    "**************"
    # 可以根据日志继续添加
)

# 如果没有指定IP，使用已知的恶意IP列表
if (-not $IPAddresses) {
    $IPAddresses = $KnownMaliciousIPs
}

Write-Host "开始处理IP地址..." -ForegroundColor Green

foreach ($IP in $IPAddresses) {
    try {
        # 验证IP地址格式
        $null = [System.Net.IPAddress]::Parse($IP)
        
        if ($Action -eq "Block") {
            # 创建阻止规则
            $RuleNameWithIP = "$RuleName`_$($IP.Replace('.', '_'))"
            
            # 检查规则是否已存在
            $ExistingRule = Get-NetFirewallRule -DisplayName $RuleNameWithIP -ErrorAction SilentlyContinue
            
            if ($ExistingRule) {
                Write-Host "规则 $RuleNameWithIP 已存在，跳过" -ForegroundColor Yellow
            } else {
                # 创建新的阻止规则
                New-NetFirewallRule -DisplayName $RuleNameWithIP `
                                   -Direction Inbound `
                                   -Protocol Any `
                                   -Action Block `
                                   -RemoteAddress $IP `
                                   -Description "阻止恶意IP $IP 的访问"
                
                Write-Host "已阻止IP: $IP" -ForegroundColor Red
            }
        }
        elseif ($Action -eq "Unblock") {
            # 移除阻止规则
            $RuleNameWithIP = "$RuleName`_$($IP.Replace('.', '_'))"
            
            $ExistingRule = Get-NetFirewallRule -DisplayName $RuleNameWithIP -ErrorAction SilentlyContinue
            
            if ($ExistingRule) {
                Remove-NetFirewallRule -DisplayName $RuleNameWithIP
                Write-Host "已解除阻止IP: $IP" -ForegroundColor Green
            } else {
                Write-Host "未找到IP $IP 的阻止规则" -ForegroundColor Yellow
            }
        }
    }
    catch {
        Write-Error "处理IP $IP 时出错: $($_.Exception.Message)"
    }
}

# 显示当前的阻止规则
Write-Host "`n当前的IP阻止规则:" -ForegroundColor Cyan
Get-NetFirewallRule -DisplayName "$RuleName*" | Select-Object DisplayName, Enabled, Direction, Action | Format-Table

Write-Host "操作完成!" -ForegroundColor Green

# 使用示例：
# .\block_malicious_ips.ps1                                    # 阻止已知恶意IP
# .\block_malicious_ips.ps1 -IPAddresses @("*******")        # 阻止指定IP
# .\block_malicious_ips.ps1 -Action Unblock                   # 解除所有阻止
# .\block_malicious_ips.ps1 -IPAddresses @("*******") -Action Unblock  # 解除指定IP
