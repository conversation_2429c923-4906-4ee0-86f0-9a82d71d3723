"""
安全管理路由
用于查看和管理安全事件、被阻止的IP等
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from app.security_config import get_blocked_ips, unblock_ip, block_ip, ip_access_log
import json
from datetime import datetime

security_admin_bp = Blueprint('security_admin', __name__)

@security_admin_bp.route('/security/dashboard')
@login_required
def dashboard():
    """安全仪表盘"""
    # 暂时移除权限检查，所有登录用户都可以访问
    # if not current_user.is_admin():
    #     flash('您没有权限访问此页面', 'error')
    #     return redirect(url_for('main.index'))

    blocked_ips = get_blocked_ips()

    # 获取访问统计
    access_stats = {}
    for ip, requests in ip_access_log.items():
        access_stats[ip] = {
            'count': len(requests),
            'last_access': max(requests) if requests else 0
        }

    # 按访问次数排序
    top_ips = sorted(access_stats.items(), key=lambda x: x[1]['count'], reverse=True)[:10]

    return render_template('security/dashboard.html',
                         blocked_ips=blocked_ips,
                         top_ips=top_ips,
                         total_blocked=len(blocked_ips))

@security_admin_bp.route('/security/blocked-ips')
@login_required
def blocked_ips():
    """被阻止的IP列表"""
    # 暂时移除权限检查
    # if not current_user.is_admin():
    #     return jsonify({'error': '权限不足'}), 403

    blocked_list = get_blocked_ips()
    return jsonify({
        'blocked_ips': blocked_list,
        'count': len(blocked_list)
    })

@security_admin_bp.route('/security/unblock-ip', methods=['POST'])
@login_required
def unblock_ip_route():
    """解除IP阻止"""
    # 暂时移除权限检查
    # if not current_user.is_admin():
    #     return jsonify({'error': '权限不足'}), 403

    data = request.get_json()
    ip = data.get('ip')

    if not ip:
        return jsonify({'error': 'IP地址不能为空'}), 400

    unblock_ip(ip)
    return jsonify({'success': True, 'message': f'IP {ip} 已解除阻止'})

@security_admin_bp.route('/security/block-ip', methods=['POST'])
@login_required
def block_ip_route():
    """手动阻止IP"""
    # 暂时移除权限检查
    # if not current_user.is_admin():
    #     return jsonify({'error': '权限不足'}), 403

    data = request.get_json()
    ip = data.get('ip')

    if not ip:
        return jsonify({'error': 'IP地址不能为空'}), 400

    # 简单的IP格式验证
    import ipaddress
    try:
        ipaddress.ip_address(ip)
    except ValueError:
        return jsonify({'error': 'IP地址格式无效'}), 400

    block_ip(ip)
    return jsonify({'success': True, 'message': f'IP {ip} 已被阻止'})

@security_admin_bp.route('/security/access-log')
@login_required
def access_log():
    """访问日志"""
    # 暂时移除权限检查
    # if not current_user.is_admin():
    #     return jsonify({'error': '权限不足'}), 403

    # 获取访问统计
    access_stats = []
    for ip, requests in ip_access_log.items():
        if requests:
            access_stats.append({
                'ip': ip,
                'count': len(requests),
                'last_access': datetime.fromtimestamp(max(requests)).strftime('%Y-%m-%d %H:%M:%S'),
                'first_access': datetime.fromtimestamp(min(requests)).strftime('%Y-%m-%d %H:%M:%S')
            })

    # 按最后访问时间排序
    access_stats.sort(key=lambda x: x['last_access'], reverse=True)

    return jsonify({
        'access_log': access_stats[:100],  # 只返回最近100条
        'total': len(access_stats)
    })

@security_admin_bp.route('/security/clear-logs', methods=['POST'])
@login_required
def clear_logs():
    """清空访问日志"""
    # 暂时移除权限检查
    # if not current_user.is_admin():
    #     return jsonify({'error': '权限不足'}), 403

    ip_access_log.clear()
    return jsonify({'success': True, 'message': '访问日志已清空'})

@security_admin_bp.route('/security/stats')
@login_required
def security_stats():
    """安全统计信息"""
    # 暂时移除权限检查
    # if not current_user.is_admin():
    #     return jsonify({'error': '权限不足'}), 403

    # 统计信息
    stats = {
        'blocked_ips_count': len(get_blocked_ips()),
        'active_sessions': len(ip_access_log),
        'total_requests': sum(len(requests) for requests in ip_access_log.values()),
        'top_ips': []
    }

    # 获取访问最频繁的IP
    ip_counts = [(ip, len(requests)) for ip, requests in ip_access_log.items()]
    ip_counts.sort(key=lambda x: x[1], reverse=True)
    stats['top_ips'] = ip_counts[:5]

    return jsonify(stats)
