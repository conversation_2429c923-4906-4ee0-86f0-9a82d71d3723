#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户引导API路由
提供引导系统的后端接口
"""

from flask import Blueprint, request, jsonify, render_template_string
from flask_login import login_required, current_user
from app.services.user_guide_service import UserGuideService

guide_api_bp = Blueprint('guide_api', __name__)

@guide_api_bp.route('/api/guide/status', methods=['GET'])
@login_required
def get_guide_status():
    """获取用户引导状态"""
    try:
        status = UserGuideService.get_user_guide_status(current_user.id)
        return jsonify({
            'success': True,
            **status
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@guide_api_bp.route('/api/guide/start', methods=['POST'])
@login_required
def start_guide():
    """开始用户引导"""
    try:
        UserGuideService.start_guide(current_user.id)
        return jsonify({
            'success': True,
            'message': '引导已开始'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@guide_api_bp.route('/api/guide/complete-step', methods=['POST'])
@login_required
def complete_step():
    """完成一个引导步骤"""
    try:
        step_name = request.form.get('step')
        if not step_name:
            return jsonify({
                'success': False,
                'message': '缺少步骤名称'
            }), 400

        next_step = UserGuideService.complete_step(current_user.id, step_name)
        return jsonify({
            'success': True,
            'next_step': next_step,
            'message': '步骤完成'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@guide_api_bp.route('/api/guide/skip', methods=['POST'])
@login_required
def skip_guide():
    """跳过引导"""
    try:
        UserGuideService.skip_guide(current_user.id)
        return jsonify({
            'success': True,
            'message': '引导已跳过'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@guide_api_bp.route('/api/guide/complete', methods=['POST'])
@login_required
def complete_guide():
    """完成整个引导"""
    try:
        UserGuideService.complete_step(current_user.id, 'completed')
        return jsonify({
            'success': True,
            'message': '引导已完成'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@guide_api_bp.route('/api/guide/restart', methods=['POST'])
@login_required
def restart_guide():
    """重新开始引导"""
    try:
        UserGuideService.start_guide(current_user.id)
        return jsonify({
            'success': True,
            'message': '引导已重新开始'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@guide_api_bp.route('/api/guide/step/<step_name>', methods=['GET'])
@login_required
def get_step_content(step_name):
    """获取步骤内容"""
    try:
        step_info = UserGuideService.get_step_info(step_name)
        step_content = UserGuideService.get_step_content(step_name)

        # 根据步骤生成HTML内容
        content_html = generate_step_html(step_name, step_content)

        return jsonify({
            'success': True,
            'title': step_info.get('title', '系统引导'),
            'content': content_html
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@guide_api_bp.route('/api/guide/create-demo/<demo_type>', methods=['POST'])
@login_required
def create_demo_data(demo_type):
    """创建演示数据"""
    try:
        if not current_user.area:
            return jsonify({
                'success': False,
                'message': '用户没有关联的区域'
            }), 400

        result = UserGuideService.generate_demo_data(demo_type, current_user.area.id)

        if result and 'error' not in result:
            return jsonify({
                'success': True,
                'message': f'演示{demo_type}创建成功',
                'data': result
            })
        else:
            return jsonify({
                'success': False,
                'message': result.get('error', '创建失败') if result else '创建失败'
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@guide_api_bp.route('/api/guide/videos/<step_name>', methods=['GET'])
@login_required
def get_step_videos(step_name):
    """获取指定步骤的视频资源"""
    try:
        from app.services.video_guide_service import VideoGuideService

        video_resources = VideoGuideService.get_video_resources(step_name)

        if video_resources:
            return jsonify({
                'success': True,
                'step_name': step_name,
                'title': video_resources.get('title', ''),
                'duration': video_resources.get('duration', ''),
                'videos': video_resources.get('videos', [])
            })
        else:
            return jsonify({
                'success': False,
                'message': '该步骤暂无视频资源'
            }), 404

    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@guide_api_bp.route('/api/guide/scenario/<school_type>', methods=['GET'])
@login_required
def get_scenario_guide(school_type):
    """获取场景化引导内容"""
    try:
        from app.services.scenario_guide_service import ScenarioGuideService

        scenario_guide = ScenarioGuideService.get_scenario_guide(school_type)
        scenario_report = ScenarioGuideService.generate_scenario_report(school_type)

        return jsonify({
            'success': True,
            'school_type': school_type,
            'scenario_guide': scenario_guide,
            'scenario_report': scenario_report
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@guide_api_bp.route('/api/guide/detect-school-type', methods=['POST'])
@login_required
def detect_school_type():
    """自动检测学校类型"""
    try:
        from app.services.scenario_guide_service import ScenarioGuideService

        school_name = request.form.get('school_name', '')
        area_info = None

        if current_user.area:
            area_info = {
                'is_township_school': getattr(current_user.area, 'is_township_school', False)
            }

        detected_type = ScenarioGuideService.detect_school_type(school_name, area_info)

        return jsonify({
            'success': True,
            'detected_type': detected_type,
            'school_name': school_name,
            'area_info': area_info
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

def generate_step_html(step_name, step_content):
    """生成步骤HTML内容"""

    if step_name == 'purchase_order':
        return render_template_string('''
        <div class="text-center mb-4">
            <i class="fas fa-shopping-cart fa-3x text-success mb-3"></i>
            <h4>采购订单管理</h4>
            <p class="text-muted">从周菜单生成采购单，规范采购流程</p>
        </div>

        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0"><i class="fas fa-route mr-2"></i>采购流程</h6>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            <div class="timeline-item">
                                <span class="timeline-marker bg-primary">1</span>
                                <div class="timeline-content">
                                    <h6>选择周菜单</h6>
                                    <p class="text-muted">从已发布的周菜单中选择需要采购的菜单</p>
                                </div>
                            </div>
                            <div class="timeline-item">
                                <span class="timeline-marker bg-info">2</span>
                                <div class="timeline-content">
                                    <h6>自动计算用量</h6>
                                    <p class="text-muted">系统根据食谱自动计算所需食材数量</p>
                                </div>
                            </div>
                            <div class="timeline-item">
                                <span class="timeline-marker bg-warning">3</span>
                                <div class="timeline-content">
                                    <h6>选择供应商</h6>
                                    <p class="text-muted">为每种食材选择合适的供应商</p>
                                </div>
                            </div>
                            <div class="timeline-item">
                                <span class="timeline-marker bg-success">4</span>
                                <div class="timeline-content">
                                    <h6>生成采购单</h6>
                                    <p class="text-muted">确认信息后生成正式采购订单</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="alert alert-info">
                    <h6><i class="fas fa-lightbulb mr-2"></i>核心价值</h6>
                    <ul class="mb-0">
                        <li>精确计算采购量</li>
                        <li>避免食材浪费</li>
                        <li>规范采购流程</li>
                        <li>建立采购档案</li>
                    </ul>
                </div>

                <div class="card mt-3">
                    <div class="card-body text-center">
                        <i class="fas fa-video fa-2x text-primary mb-2"></i>
                        <h6>视频演示</h6>
                        <button class="btn btn-sm btn-primary" onclick="playVideo('purchase_order')">
                            <i class="fas fa-play mr-1"></i>观看演示
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <a href="{{ url_for('purchase_order.index') }}" class="btn btn-success" target="_blank">
                <i class="fas fa-external-link-alt mr-1"></i>开始创建采购单
            </a>
        </div>
        ''')

    elif step_name == 'stock_in':
        return render_template_string('''
        <div class="text-center mb-4">
            <i class="fas fa-truck-loading fa-3x text-info mb-3"></i>
            <h4>食材入库管理</h4>
            <p class="text-muted">记录入库信息，建立完整的溯源档案</p>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0"><i class="fas fa-clipboard-check mr-2"></i>入库检查要点</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success mr-2"></i>检查食材外观质量</li>
                            <li><i class="fas fa-check text-success mr-2"></i>核对数量和规格</li>
                            <li><i class="fas fa-check text-success mr-2"></i>记录生产日期和保质期</li>
                            <li><i class="fas fa-check text-success mr-2"></i>拍照留存验收记录</li>
                            <li><i class="fas fa-check text-success mr-2"></i>选择合适的储存位置</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-warning text-white">
                        <h6 class="mb-0"><i class="fas fa-exclamation-triangle mr-2"></i>先入库的重要意义</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li><i class="fas fa-shield-alt text-primary mr-2"></i><strong>食品安全：</strong>确保食材质量可控</li>
                            <li><i class="fas fa-search text-primary mr-2"></i><strong>溯源追踪：</strong>建立完整的来源档案</li>
                            <li><i class="fas fa-chart-line text-primary mr-2"></i><strong>库存管理：</strong>实时掌握库存状况</li>
                            <li><i class="fas fa-calendar-alt text-primary mr-2"></i><strong>保质期管理：</strong>避免过期食材使用</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="alert alert-success mt-3">
            <h6><i class="fas fa-star mr-2"></i>系统特色</h6>
            <div class="row">
                <div class="col-md-6">
                    <ul class="mb-0">
                        <li>支持批量入库操作</li>
                        <li>自动生成入库PDF报告</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <ul class="mb-0">
                        <li>智能储存位置推荐</li>
                        <li>保质期预警提醒</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <button class="btn btn-info mr-2" onclick="createDemoStockIn()">
                <i class="fas fa-plus mr-1"></i>创建演示入库
            </button>
            <a href="{{ url_for('stock.stock_in_list') }}" class="btn btn-outline-info" target="_blank">
                <i class="fas fa-external-link-alt mr-1"></i>查看入库管理
            </a>
        </div>
        ''')

    elif step_name == 'consumption_plan':
        return render_template_string('''
        <div class="text-center mb-4">
            <i class="fas fa-calculator fa-3x text-warning mb-3"></i>
            <h4>消耗量计划</h4>
            <p class="text-muted">制定精确的食材使用计划，控制成本和浪费</p>
        </div>

        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-warning text-white">
                        <h6 class="mb-0"><i class="fas fa-chart-pie mr-2"></i>计划制定流程</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-calendar mr-2"></i>按日计划</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-dot-circle text-primary mr-2"></i>根据当日菜单</li>
                                    <li><i class="fas fa-dot-circle text-primary mr-2"></i>计算用餐人数</li>
                                    <li><i class="fas fa-dot-circle text-primary mr-2"></i>确定消耗量</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-calendar-week mr-2"></i>按周计划</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-dot-circle text-success mr-2"></i>整周菜单分析</li>
                                    <li><i class="fas fa-dot-circle text-success mr-2"></i>食材统筹安排</li>
                                    <li><i class="fas fa-dot-circle text-success mr-2"></i>优化采购计划</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="alert alert-warning">
                    <h6><i class="fas fa-target mr-2"></i>核心目标</h6>
                    <ul class="mb-0">
                        <li>精确控制用量</li>
                        <li>减少食材浪费</li>
                        <li>优化成本控制</li>
                        <li>提高管理效率</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <a href="{{ url_for('consumption.index') }}" class="btn btn-warning" target="_blank">
                <i class="fas fa-external-link-alt mr-1"></i>制定消耗计划
            </a>
        </div>
        ''')

    elif step_name == 'stock_out':
        return render_template_string('''
        <div class="text-center mb-4">
            <i class="fas fa-sign-out-alt fa-3x text-danger mb-3"></i>
            <h4>食材出库管理</h4>
            <p class="text-muted">记录出库信息，完善溯源链条</p>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-danger text-white">
                        <h6 class="mb-0"><i class="fas fa-list-check mr-2"></i>出库操作要点</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success mr-2"></i>按照消耗计划出库</li>
                            <li><i class="fas fa-check text-success mr-2"></i>遵循先进先出原则</li>
                            <li><i class="fas fa-check text-success mr-2"></i>记录出库时间和用途</li>
                            <li><i class="fas fa-check text-success mr-2"></i>更新库存数量</li>
                            <li><i class="fas fa-check text-success mr-2"></i>关联具体菜品制作</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0"><i class="fas fa-link mr-2"></i>溯源链条完善</h6>
                    </div>
                    <div class="card-body">
                        <div class="text-center">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="badge badge-primary">采购</span>
                                <i class="fas fa-arrow-right"></i>
                                <span class="badge badge-info">入库</span>
                                <i class="fas fa-arrow-right"></i>
                                <span class="badge badge-warning">储存</span>
                            </div>
                            <div class="d-flex justify-content-center align-items-center">
                                <i class="fas fa-arrow-down"></i>
                            </div>
                            <div class="d-flex justify-content-between align-items-center mt-2">
                                <span class="badge badge-danger">出库</span>
                                <i class="fas fa-arrow-right"></i>
                                <span class="badge badge-success">制作</span>
                                <i class="fas fa-arrow-right"></i>
                                <span class="badge badge-dark">餐桌</span>
                            </div>
                        </div>
                        <p class="text-center mt-3 mb-0 small text-muted">完整的食品安全追溯链</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <a href="{{ url_for('stock.stock_out_list') }}" class="btn btn-danger" target="_blank">
                <i class="fas fa-external-link-alt mr-1"></i>执行出库操作
            </a>
        </div>
        ''')

    elif step_name == 'traceability':
        return render_template_string('''
        <div class="text-center mb-4">
            <i class="fas fa-search-location fa-3x text-primary mb-3"></i>
            <h4>食材溯源管理</h4>
            <p class="text-muted">实现从采购到餐桌的全程追溯</p>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0"><i class="fas fa-route mr-2"></i>完整溯源链条</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 text-center">
                                <div class="card border-primary">
                                    <div class="card-body">
                                        <i class="fas fa-truck fa-2x text-primary mb-2"></i>
                                        <h6>供应商信息</h6>
                                        <ul class="list-unstyled small">
                                            <li>供应商资质</li>
                                            <li>联系方式</li>
                                            <li>供货记录</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 text-center">
                                <div class="card border-info">
                                    <div class="card-body">
                                        <i class="fas fa-clipboard-list fa-2x text-info mb-2"></i>
                                        <h6>采购记录</h6>
                                        <ul class="list-unstyled small">
                                            <li>采购时间</li>
                                            <li>采购数量</li>
                                            <li>质量检查</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 text-center">
                                <div class="card border-warning">
                                    <div class="card-body">
                                        <i class="fas fa-warehouse fa-2x text-warning mb-2"></i>
                                        <h6>储存记录</h6>
                                        <ul class="list-unstyled small">
                                            <li>入库时间</li>
                                            <li>储存位置</li>
                                            <li>保质期限</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 text-center">
                                <div class="card border-success">
                                    <div class="card-body">
                                        <i class="fas fa-utensils fa-2x text-success mb-2"></i>
                                        <h6>使用记录</h6>
                                        <ul class="list-unstyled small">
                                            <li>出库时间</li>
                                            <li>使用菜品</li>
                                            <li>制作人员</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="alert alert-success mt-3">
            <h6><i class="fas fa-star mr-2"></i>溯源系统特色</h6>
            <div class="row">
                <div class="col-md-6">
                    <ul class="mb-0">
                        <li><strong>一键查询：</strong>输入食材批次即可查看完整链条</li>
                        <li><strong>图形化展示：</strong>直观的溯源路径图</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <ul class="mb-0">
                        <li><strong>问题定位：</strong>快速定位食品安全问题源头</li>
                        <li><strong>责任追究：</strong>明确各环节责任人</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <button class="btn btn-primary mr-2" onclick="demoTraceability()">
                <i class="fas fa-search mr-1"></i>演示溯源查询
            </button>
            <a href="{{ url_for('traceability.index') }}" class="btn btn-outline-primary" target="_blank">
                <i class="fas fa-external-link-alt mr-1"></i>食材溯源系统
            </a>
        </div>
        ''')

    elif step_name == 'food_samples':
        return render_template_string('''
        <div class="text-center mb-4">
            <i class="fas fa-vial fa-3x text-secondary mb-3"></i>
            <h4>留样记录管理</h4>
            <p class="text-muted">一键生成留样记录，确保食品安全</p>
        </div>

        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h6 class="mb-0"><i class="fas fa-clipboard-check mr-2"></i>留样管理要求</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-clock mr-2"></i>时间要求</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success mr-2"></i>每餐必须留样</li>
                                    <li><i class="fas fa-check text-success mr-2"></i>保存48小时以上</li>
                                    <li><i class="fas fa-check text-success mr-2"></i>及时更新记录</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-weight mr-2"></i>数量要求</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success mr-2"></i>每样不少于125g</li>
                                    <li><i class="fas fa-check text-success mr-2"></i>分别密封保存</li>
                                    <li><i class="fas fa-check text-success mr-2"></i>标注详细信息</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="alert alert-info">
                    <h6><i class="fas fa-shield-alt mr-2"></i>安全保障</h6>
                    <ul class="mb-0">
                        <li>食品安全事故调查</li>
                        <li>问题食品快速定位</li>
                        <li>法律责任明确</li>
                        <li>监管部门检查</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header bg-success text-white">
                <h6 class="mb-0"><i class="fas fa-magic mr-2"></i>一键生成功能</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>自动生成内容：</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success mr-2"></i>当日菜品清单</li>
                            <li><i class="fas fa-check text-success mr-2"></i>留样时间记录</li>
                            <li><i class="fas fa-check text-success mr-2"></i>责任人信息</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>生成格式：</h6>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-file-pdf text-danger mr-2"></i>PDF格式报告</li>
                            <li><i class="fas fa-print text-primary mr-2"></i>支持直接打印</li>
                            <li><i class="fas fa-archive text-warning mr-2"></i>自动归档保存</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <button class="btn btn-secondary mr-2" onclick="generateSampleRecord()">
                <i class="fas fa-magic mr-1"></i>一键生成留样记录
            </button>
            <a href="{{ url_for('food_sample.index') }}" class="btn btn-outline-secondary" target="_blank">
                <i class="fas fa-external-link-alt mr-1"></i>留样记录管理
            </a>
        </div>
        ''')

    elif step_name == 'completed':
        return render_template_string('''
        <div class="text-center mb-4">
            <i class="fas fa-graduation-cap fa-4x text-success mb-3"></i>
            <h4>🎉 恭喜您完成了系统引导！</h4>
            <p class="text-muted">您已经掌握了完整的食堂管理流程</p>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card border-success">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0"><i class="fas fa-check-circle mr-2"></i>您已掌握的技能</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success mr-2"></i>日常管理模块操作</li>
                            <li><i class="fas fa-check text-success mr-2"></i>供应商档案管理</li>
                            <li><i class="fas fa-check text-success mr-2"></i>食材食谱创建</li>
                            <li><i class="fas fa-check text-success mr-2"></i>周菜单制定</li>
                            <li><i class="fas fa-check text-success mr-2"></i>采购订单管理</li>
                            <li><i class="fas fa-check text-success mr-2"></i>入库出库操作</li>
                            <li><i class="fas fa-check text-success mr-2"></i>食材溯源查询</li>
                            <li><i class="fas fa-check text-success mr-2"></i>留样记录生成</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card border-primary">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0"><i class="fas fa-rocket mr-2"></i>下一步建议</h6>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li class="mb-2">开始创建真实的供应商档案</li>
                            <li class="mb-2">添加学校常用的食材和食谱</li>
                            <li class="mb-2">制定下周的菜单计划</li>
                            <li class="mb-2">邀请食堂工作人员加入系统</li>
                            <li class="mb-2">设置各种提醒和预警</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>

        <div class="alert alert-info mt-3">
            <h6><i class="fas fa-lightbulb mr-2"></i>温馨提示</h6>
            <p class="mb-2">如果在使用过程中遇到任何问题，您可以：</p>
            <div class="row">
                <div class="col-md-6">
                    <ul class="mb-0">
                        <li>查看在线帮助文档</li>
                        <li>重新观看操作视频</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <ul class="mb-0">
                        <li>联系技术支持</li>
                        <li>重新开始系统引导</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <a href="{{ url_for('main.index') }}" class="btn btn-success btn-lg mr-3">
                <i class="fas fa-home mr-1"></i>开始使用系统
            </a>
            <button class="btn btn-outline-primary" onclick="userGuide.restartGuide()">
                <i class="fas fa-redo mr-1"></i>重新开始引导
            </button>
        </div>
        ''')

    elif step_name == 'ingredients_recipes':
        return render_template_string('''
        <div class="text-center mb-4">
            <i class="fas fa-carrot fa-3x text-warning mb-3"></i>
            <h4>食材食谱管理</h4>
            <p class="text-muted">建立食材数据库，创建营养食谱</p>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-warning text-white">
                        <h6 class="mb-0"><i class="fas fa-apple-alt mr-2"></i>食材管理</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success mr-2"></i>建立食材基础档案</li>
                            <li><i class="fas fa-check text-success mr-2"></i>设置营养成分信息</li>
                            <li><i class="fas fa-check text-success mr-2"></i>管理储存条件要求</li>
                            <li><i class="fas fa-check text-success mr-2"></i>设置保质期提醒</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0"><i class="fas fa-utensils mr-2"></i>食谱管理</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success mr-2"></i>创建营养均衡食谱</li>
                            <li><i class="fas fa-check text-success mr-2"></i>计算营养成分含量</li>
                            <li><i class="fas fa-check text-success mr-2"></i>设置制作工艺流程</li>
                            <li><i class="fas fa-check text-success mr-2"></i>管理食谱成本核算</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="alert alert-info mt-3">
            <h6><i class="fas fa-lightbulb mr-2"></i>操作建议</h6>
            <p class="mb-2">1. 先添加基础食材（如大米、面粉、蔬菜等）</p>
            <p class="mb-2">2. 创建简单食谱（如白米饭、青菜汤等）</p>
            <p class="mb-0">3. 体验食材的添加、编辑、删除功能</p>
        </div>

        <div class="text-center mt-4">
            <button class="btn btn-warning mr-2" onclick="createDemoIngredients()">
                <i class="fas fa-plus mr-1"></i>创建演示食材
            </button>
            <a href="{{ url_for('ingredient.index') }}" class="btn btn-outline-warning" target="_blank">
                <i class="fas fa-external-link-alt mr-1"></i>食材管理
            </a>
            <a href="{{ url_for('recipe.index') }}" class="btn btn-outline-info ml-2" target="_blank">
                <i class="fas fa-external-link-alt mr-1"></i>食谱管理
            </a>
        </div>
        ''')

    elif step_name == 'weekly_menu':
        return render_template_string('''
        <div class="text-center mb-4">
            <i class="fas fa-calendar-week fa-3x text-primary mb-3"></i>
            <h4>周菜单计划</h4>
            <p class="text-muted">制定一周的菜单安排，合理搭配营养</p>
        </div>

        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-calendar mr-2"></i>菜单制定流程</h6>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            <div class="timeline-item">
                                <span class="timeline-marker bg-primary">1</span>
                                <div class="timeline-content">
                                    <h6>创建周菜单计划</h6>
                                    <p class="text-muted">选择周期，设置基本信息</p>
                                </div>
                            </div>
                            <div class="timeline-item">
                                <span class="timeline-marker bg-info">2</span>
                                <div class="timeline-content">
                                    <h6>进入编辑状态</h6>
                                    <p class="text-muted">点击编辑按钮，开始安排菜单</p>
                                </div>
                            </div>
                            <div class="timeline-item">
                                <span class="timeline-marker bg-success">3</span>
                                <div class="timeline-content">
                                    <h6>安排每日菜品</h6>
                                    <p class="text-muted">为每天的早中晚餐选择食谱</p>
                                </div>
                            </div>
                            <div class="timeline-item">
                                <span class="timeline-marker bg-warning">4</span>
                                <div class="timeline-content">
                                    <h6>发布菜单</h6>
                                    <p class="text-muted">确认无误后发布菜单计划</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle mr-2"></i>重要提醒</h6>
                    <p class="mb-2">创建周菜单后，需要：</p>
                    <ol class="mb-0">
                        <li>返回列表页面</li>
                        <li>点击"编辑"按钮</li>
                        <li>进入编辑状态</li>
                        <li>安排具体菜品</li>
                    </ol>
                </div>

                <div class="card mt-3">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-pie fa-2x text-success mb-2"></i>
                        <h6>营养搭配</h6>
                        <p class="small text-muted">系统会自动计算营养成分，帮助您合理搭配</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <a href="{{ url_for('weekly_menu_v2.index') }}" class="btn btn-primary" target="_blank">
                <i class="fas fa-external-link-alt mr-1"></i>开始制定周菜单
            </a>
        </div>
        ''')

    # 其他步骤的HTML内容...
    return f'<p>步骤 {step_name} 的内容正在开发中...</p>'
