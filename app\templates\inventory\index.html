{% extends 'base.html' %}

{% block title %}库存管理{% endblock %}

{% block content %}
<div class="container-fluid">

    <!-- 精简的库存管理工具栏 -->
    <div class="compact-toolbar d-flex justify-content-between align-items-center">
        <!-- 左侧：页面标题 -->
        <div class="d-flex align-items-center">
            <h4 class="mb-0 mr-3">📦 库存管理</h4>
            <div class="btn-group btn-group-sm">
                <a href="{{ url_for('inventory.index', view_type='detail') }}"
                   class="btn {% if view_type == 'detail' %}btn-primary{% else %}btn-outline-primary{% endif %}">
                    <i class="fas fa-list"></i> 详细
                </a>
                <a href="{{ url_for('inventory.index', view_type='summary') }}"
                   class="btn {% if view_type == 'summary' %}btn-primary{% else %}btn-outline-primary{% endif %}">
                    <i class="fas fa-chart-pie"></i> 汇总
                </a>
            </div>
        </div>

        <!-- 右侧：快捷操作 -->
        <div class="d-flex align-items-center">
            <a href="{{ url_for('inventory.check_expiry') }}" class="btn btn-warning btn-sm mr-2">
                <i class="fas fa-exclamation-triangle"></i> 临期检查
            </a>
            <a href="{{ url_for('stock_out.index') }}" class="btn btn-outline-secondary btn-sm mr-2">
                <i class="fas fa-sign-out-alt"></i> 出库
            </a>
            <a href="{{ url_for('weekly_menu_v2.index') }}" class="btn btn-outline-primary btn-sm">
                <i class="fas fa-calendar-alt"></i> 菜单计划
            </a>
            <button type="button" class="btn btn-outline-secondary btn-sm ml-2" data-toggle="collapse" data-target="#filterForm">
                <i class="fas fa-filter"></i> 筛选
            </button>
        </div>
    </div>

    <!-- 可折叠的筛选区域 -->
    <div class="collapse mb-3" id="filterForm">
        <div class="filter-collapse">
            <form method="get" action="{{ url_for('inventory.index') }}">
                <input type="hidden" name="view_type" value="{{ view_type }}">
                <div class="row">
                    <div class="col-md-2">
                        <select name="warehouse_id" class="form-control form-control-sm" id="warehouse_id" onchange="loadStorageLocations()">
                            <option value="">全部仓库</option>
                            {% for warehouse in warehouses %}
                            <option value="{{ warehouse.id }}" {% if warehouse_id == warehouse.id %}selected{% endif %}>{{ warehouse.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select name="storage_location_id" class="form-control form-control-sm" id="storage_location_id">
                            <option value="">全部位置</option>
                            {% for location in storage_locations %}
                            <option value="{{ location.id }}" {% if storage_location_id == location.id %}selected{% endif %}>{{ location.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select name="ingredient_id" class="form-control form-control-sm">
                            <option value="">全部食材</option>
                            {% for ingredient in ingredients %}
                            <option value="{{ ingredient.id }}" {% if ingredient_id == ingredient.id %}selected{% endif %}>{{ ingredient.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select name="status" class="form-control form-control-sm">
                            <option value="">全部状态</option>
                            <option value="正常" {% if status == '正常' %}selected{% endif %}>正常</option>
                            <option value="待检" {% if status == '待检' %}selected{% endif %}>待检</option>
                            <option value="冻结" {% if status == '冻结' %}selected{% endif %}>冻结</option>
                            <option value="已过期" {% if status == '已过期' %}selected{% endif %}>已过期</option>
                            <option value="已用完" {% if status == '已用完' %}selected{% endif %}>已用完</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select name="expiry_days" class="form-control form-control-sm">
                            <option value="">全部期限</option>
                            <option value="7" {% if expiry_days == 7 %}selected{% endif %}>7天内过期</option>
                            <option value="15" {% if expiry_days == 15 %}selected{% endif %}>15天内过期</option>
                            <option value="30" {% if expiry_days == 30 %}selected{% endif %}>30天内过期</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary btn-sm mr-1">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                        <a href="{{ url_for('inventory.index') }}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-times"></i> 清除
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 直接显示表格，无外层卡片 -->
    <div class="table-responsive">
        <table class="table table-compact table-hover table-bordered">
            <thead class="thead-light">
                <tr>
                    <th style="width: 15%;">🥬 食材名称</th>
                    <th style="width: 10%;">仓库</th>
                    <th style="width: 12%;">存储位置</th>
                    <th style="width: 12%;">批次号</th>
                    <th style="width: 8%;">数量</th>
                    <th style="width: 6%;">单位</th>
                    <th style="width: 10%;">生产日期</th>
                    <th style="width: 10%;">过期日期</th>
                    <th style="width: 8%;">状态</th>
                    <th style="width: 9%;">操作</th>
                </tr>
            </thead>
                        <tbody>
                            {% for inventory in inventories %}
                            <tr>
                                <td>
                                    <div class="ingredient-highlight">{{ inventory.ingredient.name }}</div>
                                    {% if inventory.ingredient.category %}
                                    <small class="ingredient-category">{{ inventory.ingredient.category.name }}</small>
                                    {% endif %}
                                </td>
                                <td>
                                    {{ inventory.warehouse.name }}
                                </td>
                                <td>
                                    {{ inventory.storage_location.name }}
                                    <br><small class="text-muted">({{ inventory.storage_location.location_code }})</small>
                                </td>
                                <td>
                                    <span class="text-monospace">{{ inventory.batch_number }}</span>
                                </td>
                                <td class="text-right">
                                    <strong class="text-primary">{{ inventory.quantity }}</strong>
                                </td>
                                <td>
                                    {{ inventory.unit }}
                                </td>
                                <td>
                                    {{ inventory.production_date|format_datetime('%m-%d') }}
                                </td>
                                <td>
                                    {{ inventory.expiry_date|format_datetime('%m-%d') }}
                                    {% if inventory.status == '已过期' %}
                                        <br><span class="badge badge-danger badge-sm">已过期</span>
                                    {% elif inventory.status == '临期' %}
                                        <br><span class="badge badge-warning badge-sm">临期</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% set status_indicator = '' %}
                                    {% if inventory.status == '正常' %}
                                        {% set status_indicator = 'sufficient' %}
                                        <span class="stock-indicator sufficient"></span><span class="badge badge-success badge-sm">正常</span>
                                    {% elif inventory.status == '待检' %}
                                        {% set status_indicator = 'low' %}
                                        <span class="stock-indicator low"></span><span class="badge badge-warning badge-sm">待检</span>
                                    {% elif inventory.status == '冻结' %}
                                        {% set status_indicator = 'critical' %}
                                        <span class="stock-indicator critical"></span><span class="badge badge-info badge-sm">冻结</span>
                                    {% elif inventory.status == '已过期' %}
                                        {% set status_indicator = 'expired' %}
                                        <span class="stock-indicator expired"></span><span class="badge badge-danger badge-sm">已过期</span>
                                    {% elif inventory.status == '已用完' %}
                                        <span class="stock-indicator expired"></span><span class="badge badge-secondary badge-sm">已用完</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group-compact">
                                        <a href="{{ url_for('inventory.detail', id=inventory.id) }}"
                                           class="btn btn-xs btn-outline-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="10" class="text-center py-4">
                                    <i class="fas fa-box-open text-muted"></i>
                                    <br><small class="text-muted">暂无库存数据</small>
                                </td>
                            </tr>
                            {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- 精简分页 -->
    {% if pagination.pages > 1 %}
    <div class="d-flex justify-content-center mt-3">
        <ul class="pagination pagination-sm">
            {% if pagination.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('inventory.index', page=pagination.prev_num, warehouse_id=warehouse_id, ingredient_id=ingredient_id, status=status, expiry_days=expiry_days, storage_location_id=storage_location_id, view_type=view_type) }}">
                    «
                </a>
            </li>
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">«</span>
            </li>
            {% endif %}

            {% for page in pagination.iter_pages() %}
                {% if page %}
                    {% if page != pagination.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('inventory.index', page=page, warehouse_id=warehouse_id, ingredient_id=ingredient_id, status=status, expiry_days=expiry_days, storage_location_id=storage_location_id, view_type=view_type) }}">
                            {{ page }}
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page }}</span>
                    </li>
                    {% endif %}
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
                {% endif %}
            {% endfor %}

            {% if pagination.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('inventory.index', page=pagination.next_num, warehouse_id=warehouse_id, ingredient_id=ingredient_id, status=status, expiry_days=expiry_days, storage_location_id=storage_location_id, view_type=view_type) }}">
                    »
                </a>
            </li>
            {% else %}
            <li class="page-item disabled">
                <span class="page-link">»</span>
            </li>
            {% endif %}
        </ul>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
    // 加载存储位置
    function loadStorageLocations() {
        const warehouseId = document.getElementById('warehouse_id').value;
        const storageLocationSelect = document.getElementById('storage_location_id');

        // 清空现有选项
        storageLocationSelect.innerHTML = '<option value="">全部</option>';

        if (!warehouseId) return;

        // 发送AJAX请求获取存储位置
        fetch(`{{ url_for('inventory.get_storage_locations') }}?warehouse_id=${warehouseId}`)
            .then(response => response.json())
            .then(data => {
                data.forEach(location => {
                    const option = document.createElement('option');
                    option.value = location.id;
                    option.textContent = location.name;
                    storageLocationSelect.appendChild(option);
                });
            })
            .catch(error => console.error('Error loading storage locations:', error));
    }
</script>
{% endblock %}
