<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='bootstrap/css/bootstrap.min.css') }}">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/fontawesome/css/all.min.css') }}">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .main-container {
            max-width: 900px;
            margin: 0 auto;
            padding: 0 15px;
        }

        .welcome-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            margin-bottom: 30px;
            border: 1px solid rgba(255,255,255,0.2);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .school-name {
            font-size: 32px;
            font-weight: 700;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 10px;
        }

        .date-info {
            font-size: 18px;
            color: #6c757d;
            font-weight: 500;
        }

        .action-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .action-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: none;
            position: relative;
            overflow: hidden;
        }

        .action-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(45deg, #667eea, #764ba2);
        }

        .action-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .action-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 16px;
            font-weight: 600;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }

        .action-btn:hover {
            background: linear-gradient(45deg, #5a6fd8, #6a4190);
            color: white;
            transform: scale(1.05);
        }

        .action-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .action-icon {
            font-size: 48px;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .action-title {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .action-desc {
            color: #6c757d;
            font-size: 14px;
            line-height: 1.5;
        }

        .info-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .info-title {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }

        .info-title i {
            margin-right: 10px;
            color: #667eea;
        }

        .rating-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .rating-item {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .rating-item i {
            font-size: 32px;
            margin-bottom: 15px;
        }

        .rating-item h6 {
            font-weight: 600;
            margin-bottom: 10px;
        }

        .footer {
            text-align: center;
            color: rgba(255,255,255,0.8);
            font-size: 14px;
            margin-top: 30px;
        }

        .notice-box {
            background: linear-gradient(45deg, #ffeaa7, #fab1a0);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #e17055;
        }

        .notice-box i {
            color: #e17055;
            margin-right: 10px;
        }

        @media (max-width: 768px) {
            .welcome-card {
                padding: 20px;
                margin: 0 10px 20px;
            }

            .school-name {
                font-size: 24px;
            }

            .action-cards {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .info-section {
                padding: 20px;
                margin: 0 10px 20px;
            }

            .rating-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- 欢迎卡片 -->
        <div class="welcome-card">
            <div class="header">
                <div class="school-name">{{ school.name }}</div>
                <div class="date-info">陪餐记录系统 - {{ today.strftime('%Y年%m月%d日') }}</div>
            </div>

            <!-- 操作卡片 -->
            <div class="action-cards">
                <div class="action-card">
                    <div class="action-icon">
                        <i class="fas fa-plus-circle"></i>
                    </div>
                    <div class="action-title">添加陪餐记录</div>
                    <div class="action-desc">记录今日陪餐体验，包括口味、卫生、服务等方面的评价</div>
                    <a href="{{ url_for('daily_management.public_add_companion', school_id=school.id) }}" class="action-btn">
                        <i class="fas fa-edit me-2"></i> 开始记录
                    </a>
                </div>

                <div class="action-card">
                    <div class="action-icon">
                        <i class="fas fa-history"></i>
                    </div>
                    <div class="action-title">历史记录</div>
                    <div class="action-desc">查看以往的陪餐记录和评价历史</div>
                    <button class="action-btn" onclick="showHistoryModal()">
                        <i class="fas fa-search me-2"></i> 查看历史
                    </button>
                </div>
            </div>

            <!-- 注意事项 -->
            <div class="notice-box">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>重要提示：</strong>陪餐记录提交后将无法修改，请确保信息准确无误。您的反馈对改进食堂服务质量非常重要。
            </div>
        </div>

        <!-- 评价说明 -->
        <div class="info-section">
            <div class="info-title">
                <i class="fas fa-star"></i>
                陪餐评价说明
            </div>
            <div class="rating-grid">
                <div class="rating-item">
                    <i class="fas fa-utensils text-primary"></i>
                    <h6>口味评分</h6>
                    <p>评价食物的味道、口感、新鲜度和营养搭配</p>
                </div>
                <div class="rating-item">
                    <i class="fas fa-broom text-success"></i>
                    <h6>卫生评分</h6>
                    <p>评价食堂环境、餐具清洁和食品卫生状况</p>
                </div>
                <div class="rating-item">
                    <i class="fas fa-concierge-bell text-warning"></i>
                    <h6>服务评分</h6>
                    <p>评价工作人员的服务态度、效率和专业性</p>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>{{ school.name }} 食堂管理系统 &copy; {{ today.year }}</p>
            <p>感谢您的参与，让我们共同营造更好的用餐环境</p>
        </div>
    </div>

    <!-- 历史记录模态框 -->
    <div class="modal fade" id="historyModal" tabindex="-1" aria-labelledby="historyModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header" style="background: linear-gradient(45deg, #667eea, #764ba2); color: white;">
                    <h5 class="modal-title" id="historyModalLabel">
                        <i class="fas fa-history me-2"></i>历史陪餐记录
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center py-5">
                    <div class="mb-4">
                        <i class="fas fa-tools" style="font-size: 64px; color: #667eea; margin-bottom: 20px;"></i>
                    </div>
                    <h4 class="mb-3">功能开发中</h4>
                    <p class="text-muted mb-4">历史陪餐记录查询功能正在开发中，敬请期待...</p>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        如需查询历史记录，请联系学校管理员
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="{{ url_for('static', filename='vendor/jquery/jquery.min.js') }}"></script>
    <!-- Bootstrap JS -->
    <script src="{{ url_for('static', filename='bootstrap/js/bootstrap.bundle.min.js') }}"></script>

    <script>
        // 显示历史记录模态框
        function showHistoryModal() {
            const modal = new bootstrap.Modal(document.getElementById('historyModal'));
            modal.show();
        }

        // 页面加载动画
        document.addEventListener('DOMContentLoaded', function() {
            // 添加淡入动画
            const cards = document.querySelectorAll('.action-card, .info-section');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });

        // 添加点击反馈效果
        document.querySelectorAll('.action-btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                // 创建波纹效果
                const ripple = document.createElement('span');
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;

                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';
                ripple.classList.add('ripple');

                this.appendChild(ripple);

                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });
    </script>

    <style>
        /* 波纹效果 */
        .action-btn {
            position: relative;
            overflow: hidden;
        }

        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }

        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
    </style>
</body>
</html>
