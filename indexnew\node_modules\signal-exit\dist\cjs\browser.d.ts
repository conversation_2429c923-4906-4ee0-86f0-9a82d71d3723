/**
 * This is a browser shim that provides the same functional interface
 * as the main node export, but it does nothing.
 * @module
 */
import type { <PERSON><PERSON> } from './index.js';
export declare const onExit: (cb: <PERSON><PERSON>, opts: {
    alwaysLast?: boolean;
}) => () => void;
export declare const load: () => void;
export declare const unload: () => void;
//# sourceMappingURL=browser.d.ts.map