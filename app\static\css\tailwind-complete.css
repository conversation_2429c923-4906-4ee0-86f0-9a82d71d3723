/* Tailwind CSS基础样式 */
*, ::before, ::after { box-sizing: border-box; border-width: 0; border-style: solid; border-color: #e5e7eb; }
::before, ::after { --tw-content: ''; }
html { line-height: 1.5; -webkit-text-size-adjust: 100%; -moz-tab-size: 4; tab-size: 4; font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif; }
body { margin: 0; line-height: inherit; }

/* 基础样式 */
.container { width: 100%; margin-left: auto; margin-right: auto; padding-left: 1rem; padding-right: 1rem; }
@media (min-width: 640px) { .container { max-width: 640px; } }
@media (min-width: 768px) { .container { max-width: 768px; } }
@media (min-width: 1024px) { .container { max-width: 1024px; } }
@media (min-width: 1280px) { .container { max-width: 1280px; } }

/* 显示 */
.block { display: block; }
.inline-block { display: inline-block; }
.flex { display: flex; }
.grid { display: grid; }
.hidden { display: none; }

/* 定位 */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.top-0 { top: 0; }
.bottom-0 { bottom: 0; }
.left-0 { left: 0; }
.right-0 { right: 0; }
.z-0 { z-index: 0; }
.z-10 { z-index: 10; }
.z-50 { z-index: 50; }

/* 尺寸 */
.w-full { width: 100%; }
.w-1 { width: 0.25rem; }
.w-2 { width: 0.5rem; }
.w-10 { width: 2.5rem; }
.w-12 { width: 3rem; }
.w-14 { width: 3.5rem; }
.w-16 { width: 4rem; }
.w-60 { width: 15rem; }
.w-64 { width: 16rem; }
.w-80 { width: 20rem; }
.h-2 { height: 0.5rem; }
.h-10 { height: 2.5rem; }
.h-12 { height: 3rem; }
.h-14 { height: 3.5rem; }
.h-16 { height: 4rem; }
.h-20 { height: 5rem; }
.h-48 { height: 12rem; }
.h-60 { height: 15rem; }
.h-64 { height: 16rem; }
.h-80 { height: 20rem; }
.h-auto { height: auto; }
.max-w-xl { max-width: 36rem; }
.max-w-3xl { max-width: 48rem; }
.max-w-5xl { max-width: 64rem; }

/* 间距 */
.m-0 { margin: 0; }
.mx-auto { margin-left: auto; margin-right: auto; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-5 { margin-bottom: 1.25rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mb-10 { margin-bottom: 2.5rem; }
.mb-16 { margin-bottom: 4rem; }
.mt-8 { margin-top: 2rem; }
.mt-12 { margin-top: 3rem; }
.ml-1 { margin-left: 0.25rem; }
.ml-2 { margin-left: 0.5rem; }
.mr-2 { margin-right: 0.5rem; }
.mr-3 { margin-right: 0.75rem; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.px-8 { padding-left: 2rem; padding-right: 2rem; }
.px-12 { padding-left: 3rem; padding-right: 3rem; }
.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.py-12 { padding-top: 3rem; padding-bottom: 3rem; }
.pt-8 { padding-top: 2rem; }
.pt-24 { padding-top: 6rem; }
.pt-32 { padding-top: 8rem; }
.pb-16 { padding-bottom: 4rem; }
.pb-24 { padding-bottom: 6rem; }

/* 布局 */
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.gap-3 { gap: 0.75rem; }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.gap-8 { gap: 2rem; }
.space-x-2 > * + * { margin-left: 0.5rem; }
.space-x-3 > * + * { margin-left: 0.75rem; }
.space-x-8 > * + * { margin-left: 2rem; }
.space-y-3 > * + * { margin-top: 0.75rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }
.space-y-12 > * + * { margin-top: 3rem; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }

/* 文本 */
.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.leading-tight { line-height: 1.25; }
.leading-relaxed { line-height: 1.625; }
.text-center { text-align: center; }
.text-white { color: #ffffff; }
.text-gray-300 { color: #d1d5db; }
.text-gray-400 { color: #9ca3af; }
.text-gray-600 { color: #4b5563; }
.text-dark { color: #1D2129; }

/* 背景 */
.bg-primary { background-color: #165DFF; }
.bg-dark { background-color: #1D2129; }
.bg-dark-blue { background-color: #0B0E2F; }
.bg-white { background-color: #ffffff; }
.bg-transparent { background-color: transparent; }

/* 边框 */
.border { border-width: 1px; }
.border-2 { border-width: 2px; }
.border-t { border-top-width: 1px; }
.border-b { border-bottom-width: 1px; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-2xl { border-radius: 1rem; }
.rounded-full { border-radius: 9999px; }

/* 阴影 */
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
.shadow-2xl { box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); }

/* 其他 */
.overflow-hidden { overflow: hidden; }
.overflow-x-hidden { overflow-x: hidden; }
.transition-all { transition: all 0.3s ease; }
.transition-colors { transition: color 0.3s ease; }
.duration-300 { transition-duration: 300ms; }
.antialiased { -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; }
.transform { transform: translateZ(0); }
.blur-3xl { filter: blur(64px); }
.backdrop-blur-sm { backdrop-filter: blur(4px); }
.backdrop-blur-md { backdrop-filter: blur(12px); }
.opacity-50 { opacity: 0.5; }
.order-1 { order: 1; }
.order-2 { order: 2; }
.order-3 { order: 3; }

/* 响应式 */
@media (min-width: 640px) {
  .sm\\:px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
}
@media (min-width: 768px) {
  .md\\:flex { display: flex; }
  .md\\:hidden { display: none; }
  .md\\:h-20 { height: 5rem; }
  .md\\:pt-32 { padding-top: 8rem; }
  .md\\:pb-24 { padding-bottom: 6rem; }
  .md\\:text-xl { font-size: 1.25rem; }
  .md\\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\\:flex-row { flex-direction: row; }
  .md\\:items-center { align-items: center; }
  .md\\:mb-0 { margin-bottom: 0; }
  .md\\:mt-0 { margin-top: 0; }
  .md\\:w-1\\/2 { width: 50%; }
  .md\\:w-12 { width: 3rem; }
  .md\\:pr-12 { padding-right: 3rem; }
  .md\\:pl-12 { padding-left: 3rem; }
  .md\\:text-right { text-align: right; }
  .md\\:order-2 { order: 2; }
  .md\\:order-3 { order: 3; }
}
@media (min-width: 1024px) {
  .lg\\:flex-row { flex-direction: row; }
  .lg\\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .lg\\:w-1\\/2 { width: 50%; }
  .lg\\:mb-0 { margin-bottom: 0; }
  .lg\\:px-8 { padding-left: 2rem; padding-right: 2rem; }
}

/* 自定义颜色 */
.text-neon-blue { color: #00BFFF; }
.text-neon-green { color: #00FF9D; }
.text-neon-purple { color: #9D4EDD; }
.text-yellow-400 { color: #facc15; }
.text-yellow-500 { color: #eab308; }

/* 背景透明度 */
.bg-dark-blue\\/80 { background-color: rgba(11, 14, 47, 0.8); }
.bg-dark-blue\\/95 { background-color: rgba(11, 14, 47, 0.95); }
.bg-dark\\/30 { background-color: rgba(29, 33, 41, 0.3); }
.bg-dark\\/50 { background-color: rgba(29, 33, 41, 0.5); }
.bg-primary\\/5 { background-color: rgba(22, 93, 255, 0.05); }
.bg-primary\\/10 { background-color: rgba(22, 93, 255, 0.1); }
.bg-primary\\/20 { background-color: rgba(22, 93, 255, 0.2); }
.bg-primary\\/30 { background-color: rgba(22, 93, 255, 0.3); }
.bg-neon-blue\\/10 { background-color: rgba(0, 191, 255, 0.1); }
.bg-neon-blue\\/20 { background-color: rgba(0, 191, 255, 0.2); }
.bg-neon-purple\\/10 { background-color: rgba(157, 78, 221, 0.1); }
.bg-neon-purple\\/20 { background-color: rgba(157, 78, 221, 0.2); }
.bg-neon-green\\/10 { background-color: rgba(0, 255, 157, 0.1); }
.bg-neon-green\\/20 { background-color: rgba(0, 255, 157, 0.2); }
.bg-white\\/10 { background-color: rgba(255, 255, 255, 0.1); }

/* 边框颜色 */
.border-primary\\/20 { border-color: rgba(22, 93, 255, 0.2); }
.border-primary\\/30 { border-color: rgba(22, 93, 255, 0.3); }
.border-neon-blue\\/30 { border-color: rgba(0, 191, 255, 0.3); }
.border-neon-blue\\/50 { border-color: rgba(0, 191, 255, 0.5); }

/* 渐变 */
.bg-gradient-to-r { background: linear-gradient(to right, var(--tw-gradient-stops)); }
.bg-gradient-to-b { background: linear-gradient(to bottom, var(--tw-gradient-stops)); }
.from-primary { --tw-gradient-from: #165DFF; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(22, 93, 255, 0)); }
.from-primary\\/5 { --tw-gradient-from: rgba(22, 93, 255, 0.05); --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(22, 93, 255, 0)); }
.from-dark-blue { --tw-gradient-from: #0B0E2F; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(11, 14, 47, 0)); }
.from-neon-blue { --tw-gradient-from: #00BFFF; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(0, 191, 255, 0)); }
.to-neon-blue { --tw-gradient-to: #00BFFF; }
.to-neon-purple { --tw-gradient-to: #9D4EDD; }
.to-dark-blue { --tw-gradient-to: #0B0E2F; }
.to-dark { --tw-gradient-to: #1D2129; }

/* 定位 */
.-top-40 { top: -10rem; }
.-left-40 { left: -10rem; }
.-bottom-40 { bottom: -10rem; }
.-right-40 { right: -10rem; }
.-left-20 { left: -5rem; }
.-right-20 { right: -5rem; }
.-left-32 { left: -8rem; }
.-right-32 { right: -8rem; }
.top-1\\/4 { top: 25%; }
.bottom-1\\/4 { bottom: 25%; }
.top-1\\/3 { top: 33.333333%; }
.bottom-1\\/3 { bottom: 33.333333%; }
.left-1\\/2 { left: 50%; }
.-translate-x-1\\/2 { transform: translateX(-50%); }

/* 响应式文本 */
.text-\\[clamp\\(2rem\\,5vw\\,3\\.5rem\\)\\] { font-size: clamp(2rem, 5vw, 3.5rem); }
.text-\\[clamp\\(1\\.8rem\\,4vw\\,2\\.8rem\\)\\] { font-size: clamp(1.8rem, 4vw, 2.8rem); }

/* 悬停效果 */
.hover\\:text-neon-blue:hover { color: #00BFFF; }
.hover\\:bg-white\\/10:hover { background-color: rgba(255, 255, 255, 0.1); }
.hover\\:bg-primary\\/30:hover { background-color: rgba(22, 93, 255, 0.3); }
.hover\\:shadow-lg:hover { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); }
.hover\\:shadow-neon-blue\\/30:hover { box-shadow: 0 10px 15px -3px rgba(0, 191, 255, 0.3); }
.group:hover .group-hover\\:bg-primary\\/30 { background-color: rgba(22, 93, 255, 0.3); }

/* 焦点效果 */
.focus\\:outline-none:focus { outline: none; }
.focus\\:ring-2:focus { box-shadow: 0 0 0 2px rgba(0, 191, 255, 0.5); }
.focus\\:ring-neon-blue:focus { box-shadow: 0 0 0 2px rgba(0, 191, 255, 0.5); }

/* 字体 */
.font-inter { font-family: 'Inter', system-ui, sans-serif; }
.font-code { font-family: 'JetBrains Mono', monospace; }
