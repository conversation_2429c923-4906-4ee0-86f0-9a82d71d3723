#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细用户分析工具
提供更详细的用户信息，包括具体的用户列表和权限详情
"""

import os
import sys
import json
from datetime import datetime
from collections import defaultdict

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app import create_app, db
    from app.models import User, Role, UserRole, AdministrativeArea
    from app.utils.permissions import parse_permissions_json
    from sqlalchemy import text
    print("模块导入成功")
except Exception as e:
    print(f"模块导入失败: {e}")
    sys.exit(1)

def detailed_user_analysis():
    """详细用户分析"""

    try:
        app = create_app()
        print("Flask应用创建成功")
    except Exception as e:
        print(f"Flask应用创建失败: {e}")
        return

    with app.app_context():
        try:
            # 测试数据库连接
            db.session.execute(text("SELECT 1")).fetchone()
            print("数据库连接成功")
        except Exception as e:
            print(f"数据库连接失败: {e}")
            return

        print("=" * 100)
        print("详细用户分析报告")
        print("=" * 100)
        print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()

        # 1. 所有用户详细信息
        print("1. 所有用户详细信息")
        print("-" * 80)

        users = User.query.order_by(User.id).all()

        for user in users:
            print(f"用户ID: {user.id}")
            print(f"用户名: {user.username}")
            print(f"真实姓名: {user.real_name or '未设置'}")
            print(f"邮箱: {user.email or '未设置'}")
            print(f"电话: {user.phone or '未设置'}")
            print(f"状态: {'启用' if user.status == 1 else '禁用'}")

            # 区域信息
            if user.area:
                print(f"所属区域: {user.area.name} (级别: {get_area_level_name(user.area.level)})")
                print(f"区域ID: {user.area_id}")
                print(f"区域级别: {user.area_level}")
            else:
                print(f"所属区域: 无")
                print(f"区域ID: {user.area_id}")
                print(f"区域级别: {user.area_level}")

            # 角色信息
            if user.roles:
                roles_info = []
                for role in user.roles:
                    roles_info.append(f"{role.name}")
                print(f"分配角色: {', '.join(roles_info)}")
            else:
                print(f"分配角色: 无")

            # 权限分析
            if user.is_admin():
                print(f"权限级别: 系统管理员 (拥有所有权限)")
            else:
                user_permissions = get_user_permissions(user)
                if user_permissions:
                    print(f"权限模块: {', '.join(user_permissions.keys())}")
                else:
                    print(f"权限模块: 无")

            print(f"创建时间: {user.created_at.strftime('%Y-%m-%d %H:%M:%S') if user.created_at else '未知'}")
            print(f"最后登录: {user.last_login.strftime('%Y-%m-%d %H:%M:%S') if user.last_login else '从未登录'}")
            print("-" * 80)

        # 2. 角色详细信息
        print("\n2. 角色详细信息")
        print("-" * 80)

        roles = Role.query.order_by(Role.id).all()

        for role in roles:
            print(f"角色ID: {role.id}")
            print(f"角色名称: {role.name}")
            print(f"角色描述: {role.description or '无描述'}")

            # 用户数量
            user_count = UserRole.query.filter_by(role_id=role.id).count()
            print(f"分配用户数: {user_count}")

            # 权限详情
            try:
                permissions = parse_permissions_json(role.permissions)
                if '*' in permissions and '*' in permissions['*']:
                    print(f"权限配置: 全部权限 (超级管理员)")
                else:
                    print(f"权限配置:")
                    for module, actions in permissions.items():
                        if '*' in actions:
                            print(f"  {module}: 全部权限")
                        else:
                            print(f"  {module}: {', '.join(actions)}")
            except Exception as e:
                print(f"权限配置: 解析失败 - {e}")

            print(f"创建时间: {role.created_at.strftime('%Y-%m-%d %H:%M:%S') if role.created_at else '未知'}")
            print("-" * 80)

        # 3. 区域详细信息
        print("\n3. 区域详细信息")
        print("-" * 80)

        areas = AdministrativeArea.query.order_by(AdministrativeArea.level, AdministrativeArea.name).all()

        for area in areas:
            print(f"区域ID: {area.id}")
            print(f"区域名称: {area.name}")
            print(f"区域代码: {area.code}")
            print(f"区域级别: {get_area_level_name(area.level)}")
            print(f"状态: {'启用' if area.status == 1 else '禁用'}")

            # 父区域
            if area.parent:
                print(f"上级区域: {area.parent.name}")
            else:
                print(f"上级区域: 无 (顶级区域)")

            # 子区域数量
            children_count = len(area.children)
            print(f"下级区域数: {children_count}")

            # 用户数量
            user_count = User.query.filter_by(area_id=area.id).count()
            print(f"关联用户数: {user_count}")

            if user_count > 0:
                area_users = User.query.filter_by(area_id=area.id).all()
                user_names = [f"{user.username}({user.real_name or '无姓名'})" for user in area_users]
                print(f"关联用户: {', '.join(user_names)}")

            print(f"创建时间: {area.created_at.strftime('%Y-%m-%d %H:%M:%S') if area.created_at else '未知'}")
            print("-" * 80)

        print("\n" + "=" * 100)
        print("详细分析完成")

def get_area_level_name(level):
    """获取区域级别名称"""
    level_names = {
        1: "县市区",
        2: "乡镇",
        3: "学校",
        4: "食堂"
    }
    return level_names.get(level, f"未知级别({level})")

def get_user_permissions(user):
    """获取用户的所有权限"""
    all_permissions = {}

    for role in user.roles:
        try:
            permissions = parse_permissions_json(role.permissions)
            for module, actions in permissions.items():
                if module not in all_permissions:
                    all_permissions[module] = set()
                if '*' in actions:
                    all_permissions[module].add('*')
                else:
                    all_permissions[module].update(actions)
        except:
            continue

    return all_permissions

if __name__ == '__main__':
    detailed_user_analysis()
