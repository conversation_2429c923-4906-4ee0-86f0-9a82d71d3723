-- 创建周菜单临时菜品表
-- 如果表不存在则创建

IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='weekly_menu_recipes_temp' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[weekly_menu_recipes_temp](
        [id] [int] IDENTITY(1,1) NOT NULL,
        [weekly_menu_id] [int] NOT NULL,
        [day_of_week] [int] NOT NULL,
        [meal_type] [nvarchar](20) NOT NULL,
        [recipe_id] [int] NULL,
        [recipe_name] [nvarchar](100) NOT NULL,
        [is_custom] [bit] NULL DEFAULT (0),
        [temp_data] [ntext] NULL,
        [created_at] [datetime2](1) NULL DEFAULT (getdate()),
        [updated_at] [datetime2](1) NULL DEFAULT (getdate()),
        CONSTRAINT [PK_weekly_menu_recipes_temp] PRIMARY KEY CLUSTERED ([id] ASC)
    ) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]

    -- 添加外键约束
    IF EXISTS (SELECT * FROM sysobjects WHERE name='weekly_menus' AND xtype='U')
    BEGIN
        ALTER TABLE [dbo].[weekly_menu_recipes_temp] 
        ADD CONSTRAINT [FK_weekly_menu_recipes_temp_weekly_menu_id] 
        FOREIGN KEY([weekly_menu_id]) REFERENCES [dbo].[weekly_menus] ([id]) 
        ON DELETE CASCADE
    END

    PRINT '周菜单临时菜品表创建成功'
END
ELSE
BEGIN
    PRINT '周菜单临时菜品表已存在'
END

-- 检查表结构是否正确
IF EXISTS (SELECT * FROM sysobjects WHERE name='weekly_menu_recipes_temp' AND xtype='U')
BEGIN
    -- 检查是否缺少列
    IF NOT EXISTS (SELECT * FROM syscolumns WHERE id = OBJECT_ID('weekly_menu_recipes_temp') AND name = 'is_custom')
    BEGIN
        ALTER TABLE [dbo].[weekly_menu_recipes_temp] ADD [is_custom] [bit] NULL DEFAULT (0)
        PRINT '添加 is_custom 列'
    END

    IF NOT EXISTS (SELECT * FROM syscolumns WHERE id = OBJECT_ID('weekly_menu_recipes_temp') AND name = 'temp_data')
    BEGIN
        ALTER TABLE [dbo].[weekly_menu_recipes_temp] ADD [temp_data] [ntext] NULL
        PRINT '添加 temp_data 列'
    END

    IF NOT EXISTS (SELECT * FROM syscolumns WHERE id = OBJECT_ID('weekly_menu_recipes_temp') AND name = 'created_at')
    BEGIN
        ALTER TABLE [dbo].[weekly_menu_recipes_temp] ADD [created_at] [datetime2](1) NULL DEFAULT (getdate())
        PRINT '添加 created_at 列'
    END

    IF NOT EXISTS (SELECT * FROM syscolumns WHERE id = OBJECT_ID('weekly_menu_recipes_temp') AND name = 'updated_at')
    BEGIN
        ALTER TABLE [dbo].[weekly_menu_recipes_temp] ADD [updated_at] [datetime2](1) NULL DEFAULT (getdate())
        PRINT '添加 updated_at 列'
    END

    PRINT '周菜单临时菜品表结构检查完成'
END
