<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>周菜单打印测试</title>
    <style>
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* A4横向打印设置 */
        @page {
            size: A4 landscape;
            margin: 1cm 0.8cm;
        }

        html, body {
            font-family: "Microsoft YaHei", "微软雅黑", SimSun, "宋体", Arial, sans-serif;
            font-size: 11px;
            line-height: 1.3;
            color: #000;
            background: white;
            width: 100%;
            height: 100%;
        }

        .container {
            width: 100%;
            max-width: none;
            padding: 0;
            margin: 0;
        }

        /* 页眉 */
        .header {
            text-align: center;
            margin-bottom: 15px;
            page-break-inside: avoid;
        }

        .title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 6px;
            color: #000;
        }

        .subtitle {
            font-size: 13px;
            color: #333;
            margin-bottom: 4px;
        }

        .print-info {
            font-size: 10px;
            color: #666;
            margin-bottom: 10px;
        }

        /* 菜单表格 */
        .menu-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
            table-layout: fixed;
        }

        .menu-table th,
        .menu-table td {
            border: 1px solid #000;
            padding: 4px;
            vertical-align: top;
            word-wrap: break-word;
        }

        .menu-table th {
            background-color: #f5f5f5;
            font-weight: bold;
            text-align: center;
            font-size: 12px;
        }

        /* 列宽设置 */
        .date-col {
            width: 10%;
            min-width: 80px;
        }

        .meal-col {
            width: 30%;
            min-width: 180px;
        }

        /* 日期单元格 */
        .date-cell {
            text-align: center;
            font-weight: bold;
            font-size: 10px;
            background-color: #fafafa;
        }

        .date-cell .weekday {
            font-size: 11px;
            margin-bottom: 2px;
        }

        .date-cell .date {
            font-size: 9px;
            color: #666;
        }

        /* 餐次单元格 */
        .meal-cell {
            text-align: left;
            padding: 3px;
            vertical-align: top;
            min-height: 50px;
        }

        .meal-list {
            list-style: none;
            margin: 0;
            padding: 0;
            display: flex;
            flex-wrap: wrap;
            gap: 2px;
            align-content: flex-start;
        }

        .meal-list li {
            display: inline-block;
            background-color: #f8f9fa;
            border: 1px solid #ccc;
            border-radius: 2px;
            padding: 2px 5px;
            margin: 1px;
            font-size: 12px;
            line-height: 1.2;
            white-space: nowrap;
            flex: 0 0 auto;
            max-width: calc(33.33% - 4px);
            overflow: hidden;
            text-overflow: ellipsis;
            box-sizing: border-box;
        }

        .meal-list li:before {
            content: none;
        }

        .no-meal {
            color: #999;
            font-style: italic;
            font-size: 9px;
            padding: 8px;
            text-align: center;
        }

        /* 页脚 */
        .footer {
            margin-top: 20px;
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            font-size: 10px;
            page-break-inside: avoid;
        }

        .signature-section {
            text-align: center;
            min-width: 120px;
        }

        .signature-label {
            margin-bottom: 25px;
            color: #666;
        }

        .signature-line {
            border-bottom: 1px solid #000;
            width: 100px;
            margin: 0 auto;
        }

        /* 打印优化 */
        @media print {
            body {
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
                font-size: 10px;
            }

            .menu-table th,
            .menu-table td {
                border: 1.5px solid #000;
                padding: 3px;
            }

            .meal-list li {
                font-size: 10px;
                padding: 2px 4px;
                max-width: calc(33.33% - 3px);
                border: 1px solid #666;
                background-color: #f5f5f5;
            }
        }

        /* 屏幕预览 */
        @media screen {
            body {
                background: #f5f5f5;
                padding: 20px;
            }

            .container {
                background: white;
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
                padding: 20px;
                margin: 0 auto;
                max-width: 1200px;
            }

            .print-button {
                position: fixed;
                top: 20px;
                right: 20px;
                background: #007bff;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 14px;
                z-index: 1000;
            }
        }
    </style>
</head>
<body>
    <button class="print-button" onclick="window.print()">打印测试</button>

    <div class="container">
        <!-- 页眉 -->
        <div class="header">
            <div class="title">测试学校周菜单计划表</div>
            <div class="subtitle">2025年05月26日 至 2025年06月01日</div>
            <div class="print-info">打印时间：2025年01月15日 10:30</div>
        </div>

        <!-- 菜单表格 -->
        <table class="menu-table">
            <thead>
                <tr>
                    <th class="date-col">日期</th>
                    <th class="meal-col">早餐</th>
                    <th class="meal-col">午餐</th>
                    <th class="meal-col">晚餐</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="date-cell">
                        <div class="weekday">周一</div>
                        <div class="date">05-26</div>
                    </td>
                    <td class="meal-cell">
                        <ul class="meal-list">
                            <li>小米粥</li>
                            <li>煮鸡蛋</li>
                            <li>咸菜</li>
                            <li>馒头</li>
                            <li>豆浆</li>
                            <li>油条</li>
                        </ul>
                    </td>
                    <td class="meal-cell">
                        <ul class="meal-list">
                            <li>红烧肉</li>
                            <li>青椒土豆丝</li>
                            <li>西红柿鸡蛋汤</li>
                            <li>米饭</li>
                            <li>凉拌黄瓜</li>
                            <li>紫菜蛋花汤</li>
                            <li>糖醋里脊</li>
                            <li>蒜蓉菠菜</li>
                        </ul>
                    </td>
                    <td class="meal-cell">
                        <ul class="meal-list">
                            <li>鱼香肉丝</li>
                            <li>麻婆豆腐</li>
                            <li>冬瓜汤</li>
                            <li>米饭</li>
                            <li>拍黄瓜</li>
                        </ul>
                    </td>
                </tr>
                <tr>
                    <td class="date-cell">
                        <div class="weekday">周二</div>
                        <div class="date">05-27</div>
                    </td>
                    <td class="meal-cell">
                        <ul class="meal-list">
                            <li>白粥</li>
                            <li>咸鸭蛋</li>
                            <li>榨菜</li>
                            <li>包子</li>
                        </ul>
                    </td>
                    <td class="meal-cell">
                        <ul class="meal-list">
                            <li>宫保鸡丁</li>
                            <li>蒜蓉西兰花</li>
                            <li>番茄鸡蛋汤</li>
                            <li>米饭</li>
                            <li>凉拌豆腐皮</li>
                            <li>红烧茄子</li>
                        </ul>
                    </td>
                    <td class="meal-cell">
                        <ul class="meal-list">
                            <li>糖醋排骨</li>
                            <li>清炒小白菜</li>
                            <li>紫菜蛋花汤</li>
                            <li>米饭</li>
                        </ul>
                    </td>
                </tr>
                <!-- 更多行... -->
            </tbody>
        </table>

        <!-- 页脚 -->
        <div class="footer">
            <div class="signature-section">
                <div class="signature-label">制定人：</div>
                <div class="signature-line"></div>
            </div>
            <div class="signature-section">
                <div class="signature-label">审核人：</div>
                <div class="signature-line"></div>
            </div>
            <div class="signature-section">
                <div class="signature-label">负责人：</div>
                <div class="signature-line"></div>
            </div>
        </div>
    </div>
</body>
</html>
