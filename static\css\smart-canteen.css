/* 智慧食堂现代化样式 */
@layer utilities {
  /* 现代化导航栏样式 */
  .smart-navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
  }

  .smart-navbar.scrolled {
    background: rgba(102, 126, 234, 0.95);
    backdrop-filter: blur(20px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  }

  .smart-navbar .navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: white !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
  }

  .smart-navbar .navbar-brand:hover {
    transform: translateY(-1px);
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
  }

  .smart-navbar .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    padding: 0.75rem 1rem !important;
    border-radius: 8px;
    margin: 0 0.25rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .smart-navbar .nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }

  .smart-navbar .nav-link:hover::before {
    left: 100%;
  }

  .smart-navbar .nav-link:hover {
    color: white !important;
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  .smart-navbar .nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    color: white !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }

  .smart-navbar .dropdown-menu {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    margin-top: 0.5rem;
    animation: dropdownFadeIn 0.3s ease;
  }

  @keyframes dropdownFadeIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .smart-navbar .dropdown-item {
    padding: 0.75rem 1.25rem;
    border-radius: 8px;
    margin: 0.25rem 0.5rem;
    transition: all 0.3s ease;
    color: #333;
    font-weight: 500;
  }

  .smart-navbar .dropdown-item:hover {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    transform: translateX(4px);
  }

  .smart-navbar .dropdown-item i {
    width: 20px;
    text-align: center;
    margin-right: 8px;
  }

  /* 通知徽章样式 */
  .notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    border: 2px solid white;
    border-radius: 50%;
    min-width: 20px;
    height: 20px;
    font-size: 0.75rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7);
    }
    70% {
      box-shadow: 0 0 0 10px rgba(255, 107, 107, 0);
    }
    100% {
      box-shadow: 0 0 0 0 rgba(255, 107, 107, 0);
    }
  }

  /* 主题切换器样式 */
  .theme-preview {
    display: inline-block;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    margin-right: 8px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
  }

  .theme-preview.primary { background: linear-gradient(135deg, #007bff, #0056b3); }
  .theme-preview.secondary { background: linear-gradient(135deg, #6c757d, #495057); }
  .theme-preview.success { background: linear-gradient(135deg, #28a745, #1e7e34); }
  .theme-preview.warning { background: linear-gradient(135deg, #ffc107, #e0a800); }
  .theme-preview.info { background: linear-gradient(135deg, #17a2b8, #117a8b); }
  .theme-preview.danger { background: linear-gradient(135deg, #dc3545, #bd2130); }
  .theme-preview.dark { background: linear-gradient(135deg, #343a40, #1d2124); }

  /* 移动端响应式 */
  @media (max-width: 991.98px) {
    .smart-navbar .navbar-collapse {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(20px);
      border-radius: 12px;
      margin-top: 1rem;
      padding: 1rem;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    }

    .smart-navbar .nav-link {
      color: #333 !important;
      margin: 0.25rem 0;
    }

    .smart-navbar .nav-link:hover {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white !important;
    }
  }

  /* 页面内容区域样式 */
  .smart-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-top: 2rem;
    padding: 2rem;
    transition: all 0.3s ease;
  }

  .smart-container:hover {
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }

  /* 卡片样式 */
  .smart-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    overflow: hidden;
  }

  .smart-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
  }

  .smart-card .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 1.5rem;
    font-weight: 600;
  }

  /* 按钮样式 */
  .smart-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 12px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .smart-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }

  .smart-btn:hover::before {
    left: 100%;
  }

  .smart-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(102, 126, 234, 0.4);
    color: white;
  }

  /* 表格样式 */
  .smart-table {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  .smart-table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 1rem;
    font-weight: 600;
  }

  .smart-table tbody tr {
    transition: all 0.3s ease;
  }

  .smart-table tbody tr:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: scale(1.01);
  }

  /* 页脚样式 */
  .smart-footer {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    padding: 2rem 0;
    margin-top: 4rem;
    border-radius: 16px 16px 0 0;
  }
}
